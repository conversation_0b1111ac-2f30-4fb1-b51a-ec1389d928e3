# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2022-05-29 20:27:33 UTC using RuboCop version 1.30.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 23
# This cop supports safe autocorrection (--autocorrect).
Layout/ClosingHeredocIndentation:
  Exclude:
    - 'spec/nanaimo/reader_spec.rb'

# Offense count: 14
# This cop supports safe autocorrection (--autocorrect).
Layout/EmptyLineAfterGuardClause:
  Exclude:
    - 'lib/nanaimo.rb'
    - 'lib/nanaimo/object.rb'
    - 'lib/nanaimo/plist.rb'
    - 'lib/nanaimo/reader.rb'
    - 'lib/nanaimo/unicode.rb'
    - 'lib/nanaimo/writer.rb'
    - 'lib/nanaimo/writer/pbxproj.rb'
    - 'lib/nanaimo/writer/xml.rb'

# Offense count: 24
# This cop supports safe autocorrection (--autocorrect).
Layout/HeredocIndentation:
  Exclude:
    - 'Rakefile'
    - 'lib/nanaimo/writer/xml.rb'
    - 'spec/nanaimo/reader_spec.rb'
    - 'spec/nanaimo/writer/xml_spec.rb'

# Offense count: 1
# Configuration parameters: AllowComments.
Lint/EmptyFile:
  Exclude:
    - 'spec/nanaimo/plist_spec.rb'

# Offense count: 2
Lint/MissingSuper:
  Exclude:
    - 'lib/nanaimo/reader.rb'
    - 'lib/nanaimo/writer.rb'

# Offense count: 2
# Configuration parameters: ForbiddenDelimiters.
# ForbiddenDelimiters: (?-mix:(^|\s)(EO[A-Z]{1}|END)(\s|$))
Naming/HeredocDelimiterNaming:
  Exclude:
    - 'lib/nanaimo/writer/xml.rb'

# Offense count: 3
# Configuration parameters: MinNameLength, AllowNamesEndingInNumbers, AllowedNames, ForbiddenNames.
# AllowedNames: at, by, db, id, in, io, ip, of, on, os, pp, to
Naming/MethodParameterName:
  Exclude:
    - 'lib/nanaimo/reader.rb'
    - 'lib/nanaimo/writer/pbxproj.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/CaseLikeIf:
  Exclude:
    - 'lib/nanaimo/object.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: compact, expanded
Style/EmptyMethod:
  Exclude:
    - 'lib/nanaimo/writer/xml.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/Encoding:
  Exclude:
    - 'nanaimo.gemspec'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
Style/ExpandPathArguments:
  Exclude:
    - 'nanaimo.gemspec'
    - 'spec/nanaimo/writer/pbxproj_spec.rb'
    - 'spec/spec_helper.rb'

# Offense count: 2
# Configuration parameters: MaxUnannotatedPlaceholdersAllowed, IgnoredMethods.
# SupportedStyles: annotated, template, unannotated
Style/FormatStringToken:
  EnforcedStyle: unannotated

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
Style/IdenticalConditionalBranches:
  Exclude:
    - 'lib/nanaimo/unicode.rb'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
Style/IfUnlessModifier:
  Exclude:
    - 'lib/nanaimo/reader.rb'
    - 'lib/nanaimo/unicode.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: literals, strict
Style/MutableConstant:
  Exclude:
    - 'lib/nanaimo/reader.rb'
    - 'lib/nanaimo/writer.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle, IgnoredMethods.
# SupportedStyles: predicate, comparison
Style/NumericPredicate:
  Exclude:
    - 'spec/**/*'
    - 'lib/nanaimo/writer.rb'

# Offense count: 8
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: PreferredDelimiters.
Style/PercentLiteralDelimiters:
  Exclude:
    - 'lib/nanaimo/reader.rb'
    - 'lib/nanaimo/unicode.rb'
    - 'spec/nanaimo/reader_spec.rb'
    - 'spec/nanaimo/unicode_spec.rb'
    - 'spec/nanaimo/writer/xml_spec.rb'
    - 'spec/nanaimo/writer_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantFreeze:
  Exclude:
    - 'lib/nanaimo/reader.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantRegexpEscape:
  Exclude:
    - 'lib/nanaimo/writer.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: MinSize.
# SupportedStyles: percent, brackets
Style/SymbolArray:
  EnforcedStyle: brackets

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, MinSize, WordRegex.
# SupportedStyles: percent, brackets
Style/WordArray:
  Exclude:
    - 'lib/nanaimo/reader.rb'

# Offense count: 22
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, AllowedPatterns, IgnoredPatterns.
# URISchemes: http, https
Layout/LineLength:
  Max: 331
