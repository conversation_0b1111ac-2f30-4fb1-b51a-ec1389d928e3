require_relative 'tunes_base'
require_relative 'application'
require_relative 'iap'
require_relative 'version_set'
require_relative 'app_version'
require_relative 'app_version_common'
require_relative 'app_submission'
require_relative 'tunes_client'
require_relative 'language_item'
require_relative 'app_status'
require_relative 'app_image'
require_relative 'app_ratings'
require_relative 'app_version_ref'
require_relative 'app_version_history'
require_relative 'app_version_states_history'
require_relative 'transit_app_file'
require_relative 'app_screenshot'
require_relative 'language_converter'
require_relative 'build'
require_relative 'build_details'
require_relative 'build_train'
require_relative 'device_type'
require_relative 'app_trailer'
require_relative 'sandbox_tester'
require_relative 'app_details'
require_relative 'pricing_tier'
require_relative 'territory'
require_relative 'availability'
require_relative 'members'
require_relative 'member'

require_relative 'app_version_promocodes'
require_relative 'app_version_generated_promocodes'
require_relative 'app_analytics'
