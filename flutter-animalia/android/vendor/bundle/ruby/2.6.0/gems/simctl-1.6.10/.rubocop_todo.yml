# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2017-09-08 10:56:22 +0200 using RuboCop version 0.49.1.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 1
Lint/HandleExceptions:
  Exclude:
    - 'spec/spec_helper.rb'

# Offense count: 1
Lint/NonLocalExitFromIterator:
  Exclude:
    - 'lib/simctl/executor.rb'

# Offense count: 1
Lint/RescueException:
  Exclude:
    - 'lib/simctl/command/reset.rb'

# Offense count: 1
Lint/ScriptPermission:
  Exclude:
    - 'Rakefile'

# Offense count: 4
Metrics/AbcSize:
  Max: 21

# Offense count: 4
# Configuration parameters: CountComments, ExcludedMethods.
Metrics/BlockLength:
  Max: 237

# Offense count: 1
# Configuration parameters: CountComments.
Metrics/ClassLength:
  Max: 200

# Offense count: 58
# Configuration parameters: AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, IgnoredPatterns.
# URISchemes: http, https
Metrics/LineLength:
  Max: 144

# Offense count: 6
# Configuration parameters: CountComments.
Metrics/MethodLength:
  Max: 38

# Offense count: 1
Style/AccessorMethodName:
  Exclude:
    - 'lib/simctl/device_settings.rb'

# Offense count: 2
Style/ClassVars:
  Exclude:
    - 'lib/simctl.rb'

# Offense count: 30
Style/Documentation:
  Enabled: false

# Offense count: 1
Style/MethodMissing:
  Exclude:
    - 'lib/simctl/device.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: AutoCorrect, EnforcedStyle, SupportedStyles.
# SupportedStyles: predicate, comparison
Style/NumericPredicate:
  Exclude:
    - 'spec/**/*'
    - 'lib/simctl/device.rb'
