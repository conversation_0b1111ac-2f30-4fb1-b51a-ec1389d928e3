-*- coding: utf-8 -*-

commit a198860c7ceba43ccee428c20bdd082f2bdaba6e
  Author:     <PERSON><PERSON><PERSON> <<EMAIL>>
  AuthorDate: 2020-01-08 15:51:35 +0900
  Commit:     <PERSON><PERSON><PERSON> <<EMAIL>>
  CommitDate: 2020-01-08 15:51:35 +0900

    Achieve version numbers from tags

commit 07e126eea667923b2d5f4a7584687cb1decd3a56
  Author:     <PERSON><PERSON> <<EMAIL>>
  AuthorDate: 2020-01-06 15:27:08 +0900
  Commit:     <PERSON><PERSON><PERSON> <<EMAIL>>
  CommitDate: 2020-01-08 15:38:19 +0900

    Add a guard for Proc#ruby2_keywords

commit ff392be2fbea77872d801ed0051c2f166dd6eee9
  Author:     <PERSON><PERSON> <<EMAIL>>
  AuthorDate: 2020-01-03 23:51:21 +0900
  Commit:     <PERSON><PERSON><PERSON> <<EMAIL>>
  CommitDate: 2020-01-08 15:38:19 +0900

    Add a shim for Proc#ruby2_keywords

commit d5d8c0c8f45102c512bb8015988116c5110b28db
  Author:     Nobuyoshi Nakada <<EMAIL>>
  AuthorDate: 2020-01-03 10:26:25 +0900
  Commit:     Nobuyoshi Nakada <<EMAIL>>
  CommitDate: 2020-01-08 15:36:44 +0900

    Check Module#ruby2_keywords arity

    It is considered a mistake, because calling this method with no
    arguments has no effect.

commit 9cf7c9791857db17afb235230059d6cbc2408e9e
  Author:     Jeremy Evans <<EMAIL>>
  AuthorDate: 2019-11-10 12:04:28 -0800
  Commit:     Nobuyoshi Nakada <<EMAIL>>
  CommitDate: 2019-12-04 16:23:33 +0900

    Fix usage example in README

    The examle warns in Ruby 2.7, and it isn't a case where you would
    want to use ruby2_keywords.

commit dcc6958efdf25045dce149bf4d0a327e8878c9dd
  Author:     Yusuke Endoh <<EMAIL>>
  AuthorDate: 2019-12-03 18:08:39 +0900
  Commit:     Nobuyoshi Nakada <<EMAIL>>
  CommitDate: 2019-12-03 18:54:26 +0900

    Update homepage to the github repository
