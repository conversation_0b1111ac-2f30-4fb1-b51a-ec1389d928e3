# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

module Google
  module Apis
    module StorageV1
      # Version of the google-apis-storage_v1 gem
      GEM_VERSION = "0.29.0"

      # Version of the code generator used to generate this client
      GENERATOR_VERSION = "0.12.0"

      # Revision of the discovery document this client was generated from
      REVISION = "20231012"
    end
  end
end
