# -*- coding: utf-8 -*- #
# frozen_string_literal: true

module Rouge
  module Lexers
    class OpenEdge < RegexLexer
      tag 'openedge'
      filenames '*.w', '*.i'
      mimetypes 'text/x-openedge'

      title "OpenEdge ABL"
      desc "The OpenEdge ABL programming language"

      # optional comment or whitespace
      ws = %r((?:\s|//.*?\n|/[*].*?[*]/)+)
      id = /[a-zA-Z_&{}][a-zA-Z0-9_\-&}]*/

      def self.keywords
        @keywords ||= Set.new %w(
          &ELSE &ELSEIF &ENDIF &GLOB &GLOBAL-DEFINE &IF &MESSAGE &SCOP
          &SCOPED-DEFINE &THEN &UNDEF &UNDEFINE &WEBSTREAM @ {&BATCH}
          {&BATCH-MODE} {&FILE-NAME} {&LINE-NUMBE} {&LINE-NUMBER} {&OPSYS}
          {&PROCESS-ARCHITECTURE} {&SEQUENCE} {&WINDOW-SYS} {&WINDOW-SYSTEM} ABS
          ABSOLUTE ABSTRACT ACCELERATOR ACCUM ACCUM ACCUMULATE ACTIVE-FORM
          ACTIVE-WINDOW ADD ADD-BUFFER ADD-CALC-COLUMN ADD-COLUMNS-FROM
          ADD-EVENTS-PROCEDURE ADD-FIELDS-FROM ADD-FIRST ADD-INDEX-FIELD
          ADD-LAST ADD-LIKE-COLUMN ADD-LIKE-FIELD ADD-LIKE-INDEX ADD-NEW-FIELD
          ADD-NEW-INDEX ADD-SCHEMA-LOCATION ADD-SUPER-PROCEDURE ADM-DATA ADVISE
          ALERT-BOX ALIAS ALL ALLOW-COLUMN-SEARCHING ALLOW-REPLICATION ALTER
          ALWAYS-ON-TOP AMBIG AMBIGUOUS ANALYZ ANALYZE AND ANSI-ONLY ANY
          ANYWHERE APPEND APPL-ALERT APPL-ALERT-BOXES APPL-CONTEXT-ID
          APPLICATION APPLY APPSERVER-INFO APPSERVER-PASSWORD APPSERVER-USERID
          ARRAY-MESSAGE AS ASC ASCENDING ASK-OVERWRITE ASSEMBLY ASSIGN
          ASYNCHRONOUS ASYNC-REQUEST-COUNT ASYNC-REQUEST-HANDLE AT
          ATTACHED-PAIRLIST ATTR ATTR-SPACE AUDIT-CONTROL AUDIT-ENABLED
          AUDIT-EVENT-CONTEXT AUDIT-POLICY AUTHENTICATION-FAILED AUTHORIZATION
          AUTO-COMP AUTO-COMPLETION AUTO-ENDKEY AUTO-END-KEY AUTO-GO AUTO-IND
          AUTO-INDENT AUTOMATIC AUTO-RESIZE AUTO-RET AUTO-RETURN
          AUTO-SYNCHRONIZE AUTO-Z AUTO-ZAP AVAIL AVAILABLE AVAILABLE-FORMATS AVE
          AVERAGE AVG BACK BACKGROUND BACKWARD BACKWARDS BASE64-DECODE
          BASE64-ENCODE BASE-ADE BASE-KEY BATCH BATCH-MODE BATCH-SIZE BEFORE-H
          BEFORE-HIDE BEGIN-EVENT-GROUP BEGINS BELL BETWEEN BGC BGCOLOR
          BIG-ENDIAN BINARY BIND BIND-WHERE BLANK BLOCK-ITERATION-DISPLAY
          BLOCK-LEV BLOCK-LEVEL BORDER-B BORDER-BOTTOM-CHARS BORDER-BOTTOM-P
          BORDER-BOTTOM-PIXELS BORDER-L BORDER-LEFT-CHARS BORDER-LEFT-P
          BORDER-LEFT-PIXELS BORDER-R BORDER-RIGHT-CHARS BORDER-RIGHT-P
          BORDER-RIGHT-PIXELS BORDER-T BORDER-TOP-CHARS BORDER-TOP-P
          BORDER-TOP-PIXELS BOX BOX-SELECT BOX-SELECTABLE BREAK BROWSE BUFFER
          BUFFER-CHARS BUFFER-COMPARE BUFFER-COPY BUFFER-CREATE BUFFER-DELETE
          BUFFER-FIELD BUFFER-HANDLE BUFFER-LINES BUFFER-NAME
          BUFFER-PARTITION-ID BUFFER-RELEASE BUFFER-VALUE BUTTON BUTTON BUTTONS
          BY BY-POINTER BY-VARIANT-POINTER CACHE CACHE-SIZE CALL CALL-NAME
          CALL-TYPE CANCEL-BREAK CANCEL-BUTTON CAN-CREATE CAN-DELETE CAN-DO
          CAN-DO-DOMAIN-SUPPORT CAN-FIND CAN-QUERY CAN-READ CAN-SET CAN-WRITE
          CAPS CAREFUL-PAINT CASE CASE-SEN CASE-SENSITIVE CAST CATCH CDECL
          CENTER CENTERED CHAINED CHARACTER_LENGTH CHARSET CHECK CHECKED CHOOSE
          CHR CLASS CLASS-TYPE CLEAR CLEAR-APPL-CONTEXT CLEAR-LOG CLEAR-SELECT
          CLEAR-SELECTION CLEAR-SORT-ARROW CLEAR-SORT-ARROWS
          CLIENT-CONNECTION-ID CLIENT-PRINCIPAL CLIENT-TTY CLIENT-TYPE
          CLIENT-WORKSTATION CLIPBOARD CLOSE CLOSE-LOG CODE CODEBASE-LOCATOR
          CODEPAGE CODEPAGE-CONVERT COL COLLATE COL-OF COLON COLON-ALIGN
          COLON-ALIGNED COLOR COLOR-TABLE COLUMN COLUMN-BGCOLOR COLUMN-DCOLOR
          COLUMN-FGCOLOR COLUMN-FONT COLUMN-LAB COLUMN-LABEL COLUMN-MOVABLE
          COLUMN-OF COLUMN-PFCOLOR COLUMN-READ-ONLY COLUMN-RESIZABLE COLUMNS
          COLUMN-SCROLLING COMBO-BOX COM-HANDLE COMMAND COMPARES COMPILE
          COMPILER COMPLETE COM-SELF CONFIG-NAME CONNECT CONNECTED CONSTRUCTOR
          CONTAINS CONTENTS CONTEXT CONTEXT-HELP CONTEXT-HELP-FILE
          CONTEXT-HELP-ID CONTEXT-POPUP CONTROL CONTROL-BOX CONTROL-FRAME
          CONVERT CONVERT-3D-COLORS CONVERT-TO-OFFS CONVERT-TO-OFFSET
          COPY-DATASET COPY-LOB COPY-SAX-ATTRIBUTES COPY-TEMP-TABLE COUNT
          COUNT-OF CPCASE CPCOLL CPINTERNAL CPLOG CPPRINT CPRCODEIN CPRCODEOUT
          CPSTREAM CPTERM CRC-VALUE CREATE CREATE-LIKE CREATE-LIKE-SEQUENTIAL
          CREATE-NODE-NAMESPACE CREATE-RESULT-LIST-ENTRY CREATE-TEST-FILE
          CURRENT CURRENT_DATE CURRENT_DATE CURRENT-CHANGED CURRENT-COLUMN
          CURRENT-ENV CURRENT-ENVIRONMENT CURRENT-ITERATION CURRENT-LANG
          CURRENT-LANGUAGE CURRENT-QUERY CURRENT-REQUEST-INFO
          CURRENT-RESPONSE-INFO CURRENT-RESULT-ROW CURRENT-ROW-MODIFIED
          CURRENT-VALUE CURRENT-WINDOW CURS CURSOR CURSOR-CHAR CURSOR-LINE
          CURSOR-OFFSET DATABASE DATA-BIND DATA-ENTRY-RET DATA-ENTRY-RETURN
          DATA-REL DATA-RELATION DATASERVERS DATASET DATASET-HANDLE DATA-SOURCE
          DATA-SOURCE-COMPLETE-MAP DATA-SOURCE-MODIFIED DATA-SOURCE-ROWID DATA-T
          DATA-TYPE DATE-F DATE-FORMAT DAY DBCODEPAGE DBCOLLATION DB-CONTEXT
          DBNAME DBPARAM DB-REFERENCES DBREST DBRESTRICTIONS DBTASKID DBTYPE
          DBVERS DBVERSION DCOLOR DDE DDE-ERROR DDE-I DDE-ID DDE-ITEM DDE-NAME
          DDE-TOPIC DEBLANK DEBU DEBUG DEBUG-ALERT DEBUGGER DEBUG-LIST DECIMALS
          DECLARE DECLARE-NAMESPACE DECRYPT DEF DEFAULT DEFAULT-BUFFER-HANDLE
          DEFAULT-BUTTON DEFAULT-COMMIT DEFAULT-EX DEFAULT-EXTENSION
          DEFAULT-NOXL DEFAULT-NOXLATE DEFAULT-VALUE DEFAULT-WINDOW DEFAUT-B
          DEFINE DEFINED DEFINE-USER-EVENT-MANAGER DEL DELEGATE DELETE DELETE
          PROCEDURE DELETE-CHAR DELETE-CURRENT-ROW DELETE-LINE
          DELETE-RESULT-LIST-ENTRY DELETE-SELECTED-ROW DELETE-SELECTED-ROWS
          DELIMITER DESC DESCENDING DESELECT-FOCUSED-ROW DESELECTION
          DESELECT-ROWS DESELECT-SELECTED-ROW DESTRUCTOR DIALOG-BOX DICT
          DICTIONARY DIR DISABLE DISABLE-AUTO-ZAP DISABLED DISABLE-DUMP-TRIGGERS
          DISABLE-LOAD-TRIGGERS DISCON DISCONNECT DISP DISPLAY DISPLAY-MESSAGE
          DISPLAY-T DISPLAY-TYPE DISTINCT DO DOMAIN-DESCRIPTION DOMAIN-NAME
          DOMAIN-TYPE DOS DOUBLE DOWN DRAG-ENABLED DROP DROP-DOWN DROP-DOWN-LIST
          DROP-FILE-NOTIFY DROP-TARGET ds-close-cursor DSLOG-MANAGER DUMP
          DYNAMIC DYNAMIC-ENUM DYNAMIC-FUNCTION DYNAMIC-INVOKE EACH ECHO EDGE
          EDGE-CHARS EDGE-P EDGE-PIXELS EDIT-CAN-PASTE EDIT-CAN-UNDO EDIT-CLEAR
          EDIT-COPY EDIT-CUT EDITING EDITOR EDIT-PASTE EDIT-UNDO ELSE EMPTY
          EMPTY-TEMP-TABLE ENABLE ENABLED-FIELDS ENCODE ENCRYPT
          ENCRYPT-AUDIT-MAC-KEY ENCRYPTION-SALT END END-DOCUMENT END-ELEMENT
          END-EVENT-GROUP END-FILE-DROP ENDKEY END-KEY END-MOVE END-RESIZE
          END-ROW-RESIZE END-USER-PROMPT ENTERED ENTITY-EXPANSION-LIMIT ENTRY
          ENUM EQ ERROR ERROR-COL ERROR-COLUMN ERROR-ROW ERROR-STACK-TRACE
          ERROR-STAT ERROR-STATUS ESCAPE ETIME EVENT EVENT-GROUP-ID
          EVENT-PROCEDURE EVENT-PROCEDURE-CONTEXT EVENTS EVENT-T EVENT-TYPE
          EXCEPT EXCLUSIVE EXCLUSIVE-ID EXCLUSIVE-LOCK EXCLUSIVE-WEB-USER
          EXECUTE EXISTS EXP EXPAND EXPANDABLE EXPLICIT EXPORT EXPORT-PRINCIPAL
          EXTENDED EXTENT EXTERNAL FALSE FETCH FETCH-SELECTED-ROW FGC FGCOLOR
          FIELD FIELD FIELDS FILE FILE-CREATE-DATE FILE-CREATE-TIME FILE-INFO
          FILE-INFORMATION FILE-MOD-DATE FILE-MOD-TIME FILENAME FILE-NAME
          FILE-OFF FILE-OFFSET FILE-SIZE FILE-TYPE FILL FILLED FILL-IN FILTERS
          FINAL FINALLY FIND FIND-BY-ROWID FIND-CASE-SENSITIVE FIND-CURRENT
          FINDER FIND-FIRST FIND-GLOBAL FIND-LAST FIND-NEXT-OCCURRENCE
          FIND-PREV-OCCURRENCE FIND-SELECT FIND-UNIQUE FIND-WRAP-AROUND FIRST
          FIRST-ASYNCH-REQUEST FIRST-CHILD FIRST-COLUMN FIRST-FORM FIRST-OBJECT
          FIRST-OF FIRST-PROC FIRST-PROCEDURE FIRST-SERVER FIRST-TAB-I
          FIRST-TAB-ITEM FIT-LAST-COLUMN FIXED-ONLY FLAT-BUTTON FLOAT FOCUS
          FOCUSED-ROW FOCUSED-ROW-SELECTED FONT FONT-TABLE FOR FORCE-FILE FORE
          FOREGROUND FORM FORMAT FORMATTE FORMATTED FORM-LONG-INPUT FORWARD
          FORWARD FORWARDS FRAGMEN FRAGMENT FRAM FRAME FRAME-COL FRAME-DB
          FRAME-DOWN FRAME-FIELD FRAME-FILE FRAME-INDE FRAME-INDEX FRAME-LINE
          FRAME-NAME FRAME-ROW FRAME-SPA FRAME-SPACING FRAME-VAL FRAME-VALUE
          FRAME-X FRAME-Y FREQUENCY FROM FROM-C FROM-CHARS FROM-CUR FROM-CURRENT
          FROM-P FROM-PIXELS FULL-HEIGHT FULL-HEIGHT-CHARS FULL-HEIGHT-P
          FULL-HEIGHT-PIXELS FULL-PATHN FULL-PATHNAME FULL-WIDTH
          FULL-WIDTH-CHARS FULL-WIDTH-P FULL-WIDTH-PIXELS FUNCTION
          FUNCTION-CALL-TYPE GATEWAY GATEWAYS GE GENERATE-MD5 GENERATE-PBE-KEY
          GENERATE-PBE-SALT GENERATE-RANDOM-KEY GENERATE-UUID GET
          GET-ATTR-CALL-TYPE GET-ATTRIBUTE-NODE GET-BINARY-DATA GET-BLUE
          GET-BLUE-VALUE GET-BROWSE-COLUMN GET-BUFFER-HANDLE GETBYTE GET-BYTE
          GET-CALLBACK-PROC-CONTEXT GET-CALLBACK-PROC-NAME GET-CGI-LIST
          GET-CGI-LONG-VALUE GET-CGI-VALUE GET-CLASS GET-CODEPAGES
          GET-COLLATIONS GET-CONFIG-VALUE GET-CURRENT GET-DOUBLE
          GET-DROPPED-FILE GET-DYNAMIC GET-ERROR-COLUMN GET-ERROR-ROW GET-FILE
          GET-FILE-NAME GET-FILE-OFFSE GET-FILE-OFFSET GET-FIRST GET-FLOAT
          GET-GREEN GET-GREEN-VALUE GET-INDEX-BY-NAMESPACE-NAME
          GET-INDEX-BY-QNAME GET-INT64 GET-ITERATION  GET-KEY-VAL GET-KEY-VALUE
          GET-LAST GET-LOCALNAME-BY-INDEX GET-LONG GET-MESSAGE GET-NEXT
          GET-NUMBER GET-POINTER-VALUE GET-PREV GET-PRINTERS GET-PROPERTY
          GET-QNAME-BY-INDEX GET-RED GET-RED-VALUE GET-REPOSITIONED-ROW
          GET-RGB-VALUE GET-SELECTED GET-SELECTED-WIDGET GET-SHORT GET-SIGNATURE
          GET-SIZE GET-STRING GET-TAB-ITEM GET-TEXT-HEIGHT GET-TEXT-HEIGHT-CHARS
          GET-TEXT-HEIGHT-P GET-TEXT-HEIGHT-PIXELS GET-TEXT-WIDTH
          GET-TEXT-WIDTH-CHARS GET-TEXT-WIDTH-P GET-TEXT-WIDTH-PIXELS
          GET-TYPE-BY-INDEX GET-TYPE-BY-NAMESPACE-NAME GET-TYPE-BY-QNAME
          GET-UNSIGNED-LONG GET-UNSIGNED-SHORT GET-URI-BY-INDEX
          GET-VALUE-BY-INDEX GET-VALUE-BY-NAMESPACE-NAME GET-VALUE-BY-QNAME
          GET-WAIT-STATE GLOBAL GO-ON GO-PEND GO-PENDING GRANT GRAPHIC-E
          GRAPHIC-EDGE GRID-FACTOR-H GRID-FACTOR-HORIZONTAL GRID-FACTOR-V
          GRID-FACTOR-VERTICAL GRID-SNAP GRID-UNIT-HEIGHT GRID-UNIT-HEIGHT-CHARS
          GRID-UNIT-HEIGHT-P GRID-UNIT-HEIGHT-PIXELS GRID-UNIT-WIDTH
          GRID-UNIT-WIDTH-CHARS GRID-UNIT-WIDTH-P GRID-UNIT-WIDTH-PIXELS
          GRID-VISIBLE GROUP GT GUID HANDLER HAS-RECORDS HAVING HEADER HEIGHT
          HEIGHT HEIGHT-CHARS HEIGHT-P HEIGHT-P HEIGHT-PIXELS HELP HEX-DECODE
          HEX-ENCODE HIDDEN HIDE HORI HORIZONTAL HOST-BYTE-ORDER HTML-CHARSET
          HTML-END-OF-LINE HTML-END-OF-PAGE HTML-FRAME-BEGIN HTML-FRAME-END
          HTML-HEADER-BEGIN HTML-HEADER-END HTML-TITLE-BEGIN HTML-TITLE-END HWND
          ICON IF IMAGE IMAGE-DOWN IMAGE-INSENSITIVE IMAGE-SIZE IMAGE-SIZE-C
          IMAGE-SIZE-CHARS IMAGE-SIZE-P IMAGE-SIZE-PIXELS IMAGE-UP
          IMMEDIATE-DISPLAY IMPLEMENTS IMPORT IMPORT-PRINCIPAL IN
          INCREMENT-EXCLUSIVE-ID INDEX INDEXED-REPOSITION INDEX-HINT
          INDEX-INFORMATION INDICATOR INFO INFORMATION IN-HANDLE INHERIT-BGC
          INHERIT-BGCOLOR INHERIT-FGC INHERIT-FGCOLOR INHERITS INIT INITIAL
          INITIAL-DIR INITIAL-FILTER INITIALIZE-DOCUMENT-TYPE INITIATE
          INNER-CHARS INNER-LINES INPUT INPUT-O INPUT-OUTPUT INPUT-VALUE INSERT
          INSERT-ATTRIBUTE INSERT-B INSERT-BACKTAB INSERT-FILE INSERT-ROW
          INSERT-STRING INSERT-T INSERT-TAB INTERFACE INTERNAL-ENTRIES INTO
          INVOKE IS IS-ATTR IS-ATTR IS-ATTR-SPACE IS-CLAS IS-CLASS IS-JSON
          IS-LEAD-BYTE IS-OPEN IS-PARAMETER-SET IS-PARTITIONE IS-PARTITIONED
          IS-ROW-SELECTED IS-SELECTED IS-XML ITEM ITEMS-PER-ROW JOIN
          JOIN-BY-SQLDB KBLABEL KEEP-CONNECTION-OPEN KEEP-FRAME-Z
          KEEP-FRAME-Z-ORDER KEEP-MESSAGES KEEP-SECURITY-CACHE KEEP-TAB-ORDER
          KEY KEYCODE KEY-CODE KEYFUNC KEY-FUNC KEYFUNCTION KEY-FUNCTION
          KEYLABEL KEY-LABEL KEYS KEYWORD KEYWORD-ALL LABEL LABEL-BGC
          LABEL-BGCOLOR LABEL-DC LABEL-DCOLOR LABEL-FGC LABEL-FGCOLOR LABEL-FONT
          LABEL-PFC LABEL-PFCOLOR LABELS LABELS-HAVE-COLONS LANDSCAPE LANGUAGE
          LANGUAGES LARGE LARGE-TO-SMALL LAST LAST-ASYNCH-REQUEST LAST-BATCH
          LAST-CHILD LAST-EVEN LAST-EVENT LAST-FORM LASTKEY LAST-KEY LAST-OBJECT
          LAST-OF LAST-PROCE LAST-PROCEDURE LAST-SERVER LAST-TAB-I LAST-TAB-ITEM
          LC LDBNAME LE LEAVE LEFT-ALIGN LEFT-ALIGNED LEFT-TRIM LENGTH LIBRARY
          LIKE LIKE-SEQUENTIAL LINE LINE-COUNT LINE-COUNTER LIST-EVENTS LISTI
          LISTING LIST-ITEM-PAIRS LIST-ITEMS LIST-PROPERTY-NAMES
          LIST-QUERY-ATTRS LIST-SET-ATTRS LIST-WIDGETS LITERAL-QUESTION
          LITTLE-ENDIAN LOAD LOAD-DOMAINS LOAD-ICON LOAD-IMAGE LOAD-IMAGE-DOWN
          LOAD-IMAGE-INSENSITIVE LOAD-IMAGE-UP LOAD-MOUSE-P LOAD-MOUSE-POINTER
          LOAD-PICTURE LOAD-SMALL-ICON LOCAL-NAME LOCAL-VERSION-INFO
          LOCATOR-COLUMN-NUMBER LOCATOR-LINE-NUMBER LOCATOR-PUBLIC-ID
          LOCATOR-SYSTEM-ID LOCATOR-TYPE LOCKED LOCK-REGISTRATION LOG
          LOG-AUDIT-EVENT LOGIN-EXPIRATION-TIMESTAMP LOGIN-HOST LOGIN-STATE
          LOG-MANAGER LOGOUT LOOKAHEAD LOOKUP LT MACHINE-CLASS MANDATORY
          MANUAL-HIGHLIGHT MAP MARGIN-EXTRA MARGIN-HEIGHT MARGIN-HEIGHT-CHARS
          MARGIN-HEIGHT-P MARGIN-HEIGHT-PIXELS MARGIN-WIDTH MARGIN-WIDTH-CHARS
          MARGIN-WIDTH-P MARGIN-WIDTH-PIXELS MARK-NEW MARK-ROW-STATE MATCHES MAX
          MAX MAX-BUTTON MAX-CHARS MAX-DATA-GUESS MAX-HEIGHT MAX-HEIGHT-C
          MAX-HEIGHT-CHARS MAX-HEIGHT-P MAX-HEIGHT-PIXELS MAXIMIZE MAXIMUM
          MAXIMUM-LEVEL MAX-ROWS MAX-SIZE MAX-VAL MAX-VALUE MAX-WIDTH MAX-WIDTH
          MAX-WIDTH-CHARS MAX-WIDTH-P MAX-WIDTH-PIXELS MD5-DIGEST MEMBER
          MEMPTR-TO-NODE-VALUE MENU MENUBAR MENU-BAR MENU-ITEM MENU-K MENU-KEY
          MENU-M MENU-MOUSE MERGE-BY-FIELD MESSAGE MESSAGE-AREA
          MESSAGE-AREA-FONT MESSAGE-LINES METHOD MIN MIN MIN-BUTTON
          MIN-COLUMN-WIDTH-C MIN-COLUMN-WIDTH-CHARS MIN-COLUMN-WIDTH-P
          MIN-COLUMN-WIDTH-PIXELS MIN-HEIGHT MIN-HEIGHT-CHARS MIN-HEIGHT-P
          MIN-HEIGHT-PIXELS MINIMUM MIN-SIZE MIN-VAL MIN-VALUE MIN-WIDTH
          MIN-WIDTH-CHARS MIN-WIDTH-P MIN-WIDTH-PIXELS MOD MODIFIED MODULO MONTH
          MOUSE MOUSE-P MOUSE-POINTER MOVABLE MOVE-AFTER MOVE-AFTER-TAB-ITEM
          MOVE-BEFOR MOVE-BEFORE-TAB-ITEM MOVE-COL MOVE-COLUMN MOVE-TO-B
          MOVE-TO-BOTTOM MOVE-TO-EOF MOVE-TO-T MOVE-TO-TOP MPE MTIME
          MULTI-COMPILE MULTIPLE MULTIPLE-KEY MULTITASKING-INTERVAL MUST-EXIST
          NAME NAMESPACE-PREFIX NAMESPACE-URI NATIVE NE NEEDS-APPSERVER-PROMPT
          NEEDS-PROMPT NEW NEW-INSTANCE NEW-ROW NEXT NEXT-COLUMN NEXT-PROMPT
          NEXT-ROWID NEXT-SIBLING NEXT-TAB-I NEXT-TAB-ITEM NEXT-VALUE NO
          NO-APPLY NO-ARRAY-MESSAGE NO-ASSIGN NO-ATTR NO-ATTR NO-ATTR-LIST
          NO-ATTR-SPACE NO-AUTO-VALIDATE NO-BIND-WHERE NO-BOX NO-CONSOLE
          NO-CONVERT NO-CONVERT-3D-COLORS NO-CURRENT-VALUE NO-DEBUG
          NODE-VALUE-TO-MEMPTR NO-DRAG NO-ECHO NO-EMPTY-SPACE NO-ERROR NO-F
          NO-FILL NO-FOCUS NO-HELP NO-HIDE NO-INDEX-HINT NO-INHERIT-BGC
          NO-INHERIT-BGCOLOR NO-INHERIT-FGC NO-INHERIT-FGCOLOR NO-JOIN-BY-SQLDB
          NO-LABEL NO-LABELS NO-LOBS NO-LOCK NO-LOOKAHEAD NO-MAP NO-MES
          NO-MESSAGE NONAMESPACE-SCHEMA-LOCATION NONE NO-PAUSE NO-PREFE
          NO-PREFETCH NORMALIZE NO-ROW-MARKERS NO-SCROLLBAR-VERTICAL
          NO-SEPARATE-CONNECTION NO-SEPARATORS NOT NO-TAB-STOP NOT-ACTIVE NO-UND
          NO-UNDERLINE NO-UNDO NO-VAL NO-VALIDATE NOW NO-WAIT NO-WORD-WRAP NULL
          NUM-ALI NUM-ALIASES NUM-BUFFERS NUM-BUT NUM-BUTTONS NUM-COL
          NUM-COLUMNS NUM-COPIES NUM-DBS NUM-DROPPED-FILES NUM-ENTRIES NUMERIC
          NUMERIC-F NUMERIC-FORMAT NUM-FIELDS NUM-FORMATS NUM-ITEMS
          NUM-ITERATIONS NUM-LINES NUM-LOCKED-COL NUM-LOCKED-COLUMNS
          NUM-MESSAGES NUM-PARAMETERS NUM-REFERENCES NUM-REPLACED NUM-RESULTS
          NUM-SELECTED NUM-SELECTED-ROWS NUM-SELECTED-WIDGETS NUM-TABS
          NUM-TO-RETAIN NUM-VISIBLE-COLUMNS OBJECT OCTET-LENGTH OF OFF OK
          OK-CANCEL OLD ON ON-FRAME ON-FRAME-BORDER OPEN OPSYS OPTION OR
          ORDERED-JOIN ORDINAL OS-APPEND OS-COMMAND OS-COPY OS-CREATE-DIR
          OS-DELETE OS-DIR OS-DRIVE OS-DRIVES OS-ERROR OS-GETENV OS-RENAME
          OTHERWISE OUTPUT OVERLAY OVERRIDE OWNER PAGE PAGE-BOT PAGE-BOTTOM
          PAGED PAGE-NUM PAGE-NUMBER PAGE-SIZE PAGE-TOP PAGE-WID PAGE-WIDTH
          PARAM PARAMETER PARENT PARSE-STATUS PARTIAL-KEY PASCAL PASSWORD-FIELD
          PATHNAME PAUSE PBE-HASH-ALG PBE-HASH-ALGORITHM PBE-KEY-ROUNDS PDBNAME
          PERSIST PERSISTENT PERSISTENT-CACHE-DISABLED PFC PFCOLOR PIXELS
          PIXELS-PER-COL PIXELS-PER-COLUMN PIXELS-PER-ROW POPUP-M POPUP-MENU
          POPUP-O POPUP-ONLY PORTRAIT POSITION PRECISION PREFER-DATASET PREPARED
          PREPARE-STRING PREPROC PREPROCESS PRESEL PRESELECT PREV PREV-COLUMN
          PREV-SIBLING PREV-TAB-I PREV-TAB-ITEM PRIMARY PRINTER
          PRINTER-CONTROL-HANDLE PRINTER-HDC PRINTER-NAME PRINTER-PORT
          PRINTER-SETUP PRIVATE PRIVATE-D PRIVATE-DATA PRIVILEGES PROCE
          PROCEDURE PROCEDURE-CALL-TYPE PROCEDURE-TYPE PROCESS PROC-HA
          PROC-HANDLE PROC-ST PROC-STATUS proc-text proc-text-buffer PROFILER
          PROGRAM-NAME PROGRESS PROGRESS-S PROGRESS-SOURCE PROMPT PROMPT-F
          PROMPT-FOR PROMSGS PROPATH PROPERTY PROTECTED PROVERS PROVERSION PROXY
          PROXY-PASSWORD PROXY-USERID PUBLIC PUBLIC-ID PUBLISH PUBLISHED-EVENTS
          PUT PUTBYTE PUT-BYTE PUT-DOUBLE PUT-FLOAT PUT-INT64 PUT-KEY-VAL
          PUT-KEY-VALUE PUT-LONG PUT-SHORT PUT-STRING PUT-UNSIGNED-LONG QUERY
          QUERY-CLOSE QUERY-OFF-END QUERY-OPEN QUERY-PREPARE QUERY-TUNING
          QUESTION QUIT QUOTER RADIO-BUTTONS RADIO-SET RANDOM RAW-TRANSFER
          RCODE-INFO RCODE-INFORMATION READ-AVAILABLE READ-EXACT-NUM READ-FILE
          READ-JSON READKEY READ-ONLY READ-XML REAL RECORD-LENGTH RECT RECTANGLE
          RECURSIVE REFERENCE-ONLY REFRESH REFRESHABLE REFRESH-AUDIT-POLICY
          REGISTER-DOMAIN RELEASE REMOTE REMOVE-EVENTS-PROCEDURE
          REMOVE-SUPER-PROCEDURE REPEAT REPLACE REPLACE-SELECTION-TEXT
          REPOSITION REPOSITION-BACKWARD REPOSITION-FORWARD REPOSITION-MODE
          REPOSITION-TO-ROW REPOSITION-TO-ROWID REQUEST REQUEST-INFO RESET
          RESIZA RESIZABLE RESIZE RESPONSE-INFO RESTART-ROW RESTART-ROWID RETAIN
          RETAIN-SHAPE RETRY RETRY-CANCEL RETURN RETURN-ALIGN RETURN-INS
          RETURN-INSERTED RETURNS RETURN-TO-START-DI RETURN-TO-START-DIR
          RETURN-VAL RETURN-VALUE RETURN-VALUE-DATA-TYPE REVERSE-FROM REVERT
          REVOKE RGB-VALUE RIGHT-ALIGNED RIGHT-TRIM R-INDEX ROLES ROUND
          ROUTINE-LEVEL ROW ROW-HEIGHT-CHARS ROW-HEIGHT-PIXELS ROW-MARKERS
          ROW-OF ROW-RESIZABLE RULE RUN RUN-PROCEDURE SAVE SAVE CACHE SAVE-AS
          SAVE-FILE SAX-COMPLE SAX-COMPLETE SAX-PARSE SAX-PARSE-FIRST
          SAX-PARSE-NEXT SAX-PARSER-ERROR SAX-RUNNING SAX-UNINITIALIZED
          SAX-WRITE-BEGIN SAX-WRITE-COMPLETE SAX-WRITE-CONTENT SAX-WRITE-ELEMENT
          SAX-WRITE-ERROR SAX-WRITE-IDLE SAX-WRITER SAX-WRITE-TAG SCHEMA
          SCHEMA-LOCATION SCHEMA-MARSHAL SCHEMA-PATH SCREEN SCREEN-IO
          SCREEN-LINES SCREEN-VAL SCREEN-VALUE SCROLL SCROLLABLE SCROLLBAR-H
          SCROLLBAR-HORIZONTAL SCROLL-BARS SCROLLBAR-V SCROLLBAR-VERTICAL
          SCROLL-DELTA SCROLLED-ROW-POS SCROLLED-ROW-POSITION SCROLLING
          SCROLL-OFFSET SCROLL-TO-CURRENT-ROW SCROLL-TO-I SCROLL-TO-ITEM
          SCROLL-TO-SELECTED-ROW SDBNAME SEAL SEAL-TIMESTAMP SEARCH SEARCH-SELF
          SEARCH-TARGER SECTION SECURITY-POLICY SEEK SELECT SELECTABLE
          SELECT-ALL SELECTED SELECT-FOCUSED-ROW SELECTION SELECTION-END
          SELECTION-LIST SELECTION-START SELECTION-TEXT SELECT-NEXT-ROW
          SELECT-PREV-ROW SELECT-ROW SELF SEND send-sql send-sql-statement
          SENSITIVE SEPARATE-CONNECTION SEPARATOR-FGCOLOR SEPARATORS
          SERIALIZABLE SERIALIZE-HIDDEN SERIALIZE-NAME SERVER
          SERVER-CONNECTION-BOUND SERVER-CONNECTION-BOUND-REQUEST
          SERVER-CONNECTION-CONTEXT SERVER-CONNECTION-ID SERVER-OPERATING-MODE
          SESSION SESSION-IDSET SET-APPL-CONTEXT SET-ATTR-CALL-TYPE
          SET-ATTRIBUTE-NODE SET-BLUE SET-BLUE-VALUE SET-BREAK SET-BUFFERS
          SET-CALLBACK SET-CLIENT SET-COMMIT SET-CONTENTS SET-CURRENT-VALUE
          SET-DB-CLIENT SET-DYNAMIC SET-EVENT-MANAGER-OPTION SET-GREEN
          SET-GREEN-VALUE SET-INPUT-SOURCE SET-OPTION SET-OUTPUT-DESTINATION
          SET-PARAMETER SET-POINTER-VALUE SET-PROPERTY SET-RED SET-RED-VALUE
          SET-REPOSITIONED-ROW SET-RGB-VALUE SET-ROLLBACK SET-SELECTION SET-SIZE
          SET-SORT-ARROW SETUSER SETUSERID SET-WAIT-STATE SHA1-DIGEST SHARE
          SHARED SHARE-LOCK SHOW-IN-TASKBAR SHOW-STAT SHOW-STATS SIDE-LAB
          SIDE-LABEL-H SIDE-LABEL-HANDLE SIDE-LABELS SIGNATURE SILENT SIMPLE
          SINGLE SINGLE-RUN SINGLETON SIZE SIZE-C SIZE-CHARS SIZE-P SIZE-PIXELS
          SKIP SKIP-DELETED-RECORD SLIDER SMALL-ICON SMALLINT SMALL-TITLE SOME
          SORT SORT-ASCENDING SORT-NUMBER SOURCE SOURCE-PROCEDURE SPACE SQL SQRT
          SSL-SERVER-NAME STANDALONE START START-DOCUMENT START-ELEMENT
          START-MOVE START-RESIZE START-ROW-RESIZE STATE-DETAIL STATIC STATUS
          STATUS-AREA STATUS-AREA-FONT STDCALL STOP STOP-AFTER STOP-PARSING
          STOPPE STOPPED STORED-PROC STORED-PROCEDURE STREAM STREAM-HANDLE
          STREAM-IO STRETCH-TO-FIT STRICT STRICT-ENTITY-RESOLUTION STRING
          STRING-VALUE STRING-XREF SUB- SUB-AVE SUB-AVERAGE SUB-COUNT
          SUB-MAXIMUM SUB-MENU SUB-MIN SUB-MINIMUM SUBSCRIBE SUBST SUBSTITUTE
          SUBSTR SUBSTRING SUB-TOTAL SUBTYPE SUM SUM-MAX SUPER SUPER-PROCEDURES
          SUPPRESS-NAMESPACE-PROCESSING SUPPRESS-W SUPPRESS-WARNINGS
          SYMMETRIC-ENCRYPTION-ALGORITHM SYMMETRIC-ENCRYPTION-IV
          SYMMETRIC-ENCRYPTION-KEY SYMMETRIC-SUPPORT SYSTEM-ALERT
          SYSTEM-ALERT-BOXES SYSTEM-DIALOG SYSTEM-HELP SYSTEM-ID TABLE
          TABLE-HANDLE TABLE-NUMBER TABLE-SCAN TAB-POSITION TAB-STOP TARGET
          TARGET-PROCEDURE TEMP-DIR TEMP-DIRECTORY TEMP-TABLE TEMP-TABLE-PREPARE
          TERM TERMINAL TERMINATETEXT TEXT TEXT-CURSOR TEXT-SEG-GROW
          TEXT-SELECTED THEN THIS-OBJECT THIS-PROCEDURE THREAD-SAFE THREE-D
          THROUGH THROW THRU TIC-MARKS TIME TIME-SOURCE TITLE TITLE-BGC
          TITLE-BGCOLOR TITLE-DC TITLE-DCOLORTITLE-FGC TITLE-FGCOLOR TITLE-FO
          TITLE-FONT TO TODAY TOGGLE-BOX TOOLTIP TOOLTIPS TOPIC TOP-NAV-QUERY
          TOP-ONLY TO-ROWID TOTAL TRAILING TRANS TRANSACTION TRANSACTION-MODE
          TRANS-INIT-PROCEDURE TRANSPARENT TRIGGER TRIGGERS TRIM TRUE TRUNC
          TRUNCATE TYPE TYPE-OF UNBOX UNBUFF UNBUFFERED UNDERL UNDERLINE UNDO
          UNFORM UNFORMATTED UNION UNIQUE UNIQUE-ID UNIQUE-MATCH UNIX
          UNLESS-HIDDEN UNLOAD UNSIGNED-LONG UNSUBSCRIBE UP UPDATE
          UPDATE-ATTRIBUTE URL URL-DECODE URL-ENCODE URL-PASSWORD URL-USERID USE
          USE-DICT-EXPS USE-FILENAME USE-INDEX USER USE-REVVIDEO USERID USER-ID
          USE-TEXT USE-UNDERLINE USE-WIDGET-POOL USING V6DISPLAY V6FRAME
          VALIDATE VALIDATE-EXPRESSION VALIDATE-MESSAGE VALIDATE-SEAL
          VALIDATION-ENABLED VALID-EVENT VALID-HANDLE VALID-OBJECT VALUE
          VALUE-CHANGED VALUES VAR VARIABLE VERBOSE VERSION VERT VERTICAL VIEW
          VIEW-AS VIEW-FIRST-COLUMN-ON-REOPEN VIRTUAL-HEIGHT
          VIRTUAL-HEIGHT-CHARS VIRTUAL-HEIGHT-P VIRTUAL-HEIGHT-PIXELS
          VIRTUAL-WIDTH VIRTUAL-WIDTH-CHARS VIRTUAL-WIDTH-P VIRTUAL-WIDTH-PIXELS
          VISIBLE VOID WAIT WAIT-FOR WARNING WEB-CONTEXT WEEKDAY WHEN WHERE
          WHILE WIDGET WIDGET-E WIDGET-ENTER WIDGET-ID WIDGET-L WIDGET-LEAVE
          WIDGET-POOL WIDTH WIDTH WIDTH-CHARS WIDTH-P WIDTH-PIXELS WINDOW
          WINDOW-MAXIM WINDOW-MAXIMIZED WINDOW-MINIM WINDOW-MINIMIZED
          WINDOW-NAME WINDOW-NORMAL WINDOW-STA WINDOW-STATE WINDOW-SYSTEM WITH
          WORD-INDEX WORD-WRAP WORK-AREA-HEIGHT-PIXELS WORK-AREA-WIDTH-PIXELS
          WORK-AREA-X WORK-AREA-Y WORKFILE WORK-TAB WORK-TABLE WRITE WRITE-CDATA
          WRITE-CHARACTERS WRITE-COMMENT WRITE-DATA-ELEMENT WRITE-EMPTY-ELEMENT
          WRITE-ENTITY-REF WRITE-EXTERNAL-DTD WRITE-FRAGMENT WRITE-JSON
          WRITE-MESSAGE WRITE-PROCESSING-INSTRUCTION WRITE-STATUS WRITE-XML
          WRITE-XMLSCHEMA X XCODE XML-DATA-TYPE XML-ENTITY-EXPANSION-LIMIT
          XML-NODE-TYPE XML-SCHEMA-PATH XML-STRICT-ENTITY-RESOLUTION
          XML-SUPPRESS-NAMESPACE-PROCESSING X-OF XREF XREF-XML Y YEAR
          YEAR-OFFSET YES YES-NO YES-NO-CANCEL Y-OF
        )
      end

      def self.keywords_type
        @keywords_type ||= Set.new %w(
          BLOB CHARACTER CHAR CLOB COM-HANDLE DATE DATETIME DATETIME-TZ DECIMAL
          DEC HANDLE INT64 INTEGER INT LOGICAL LONGCHAR MEMPTR RAW RECID ROWID
          WIDGET-HANDLE
        )
      end

      state :root do
        rule %r(\s+), Text

        rule %r((\.)(\s+)) do
          groups Operator, Text
        end

        rule %r(//[^\n]*), Comment::Single
        rule %r(/[*].*?[*]/)m, Comment::Multiline

        rule %r/(\{?&)(\S+)/ do
          groups Comment::Preproc, Name::Other
          push :preproc
        end

        rule %r/"/, Str, :string
        rule %r('(\\.|\\[0-7]{1,3}|\\x[a-f0-9]{1,2}|[^\\'\n])')i, Str::Char

        mixin :numbers

        rule %r(\*/), Error

        rule %r([~!%^*+=\|?:<>/-]), Operator
        rule %r/[()\[\],]/, Punctuation

        rule %r/(?:TRUE|FALSE|NULL)\b/i, Keyword::Constant

        rule id do |m|
          name = m[0].upcase

          if self.class.keywords.include? name
            token Name::Builtin
          elsif self.class.keywords_type.include? name
            token Keyword::Type
          else
            token Name::Variable
          end
        end
      end

      state :numbers do
        rule %r((?:\d+[.]\d*|[.]\d+)(?:e[+-]?\d+[lu]*)?)i, Num::Float
        rule %r(\d+e[+-]?\d+[lu]*)i, Num::Float
        rule %r/0x[0-9a-f]+[lu]*/i, Num::Hex
        rule %r/0[0-7]+[lu]*/i, Num::Oct
        rule %r/\d+[lu]*/i, Num::Integer
      end

      state :string do
        rule %r/"/, Str, :pop!
        rule %r/\\([\\abfnrtv"']|x[a-fA-F0-9]{2,4}|[0-7]{1,3})/, Str::Escape
        rule %r/[^\\"]+/, Str
        rule %r/\\/, Str # stray backslash
      end

      state :preproc do
        rule %r/\n/, Text, :pop!
        rule %r/\s+/, Text

        rule %r/({?&)(\S+)/ do
          groups Comment::Preproc, Name::Other
        end

        rule %r/"/, Str, :string
        mixin :numbers

        rule %r/\S+/, Name
      end
    end
  end
end
