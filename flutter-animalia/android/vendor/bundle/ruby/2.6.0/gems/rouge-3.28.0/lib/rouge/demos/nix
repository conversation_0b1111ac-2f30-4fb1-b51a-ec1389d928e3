# See https://nixos.org/nix/manual/#sec-expression-syntax
{ stdenv, fetchurl, perl }: # 1

stdenv.mkDerivation { # 2
  name = "hello-2.1.1"; # 3
  builder = ./builder.sh; # 4
  meta = rec {
    name = "rouge";
    version = "${name}-2.1.1";
    number = 55 + 12;
    isSmaller = number < 42;
    bool = true;
  };
  src = fetchurl { # 5
    url = ftp://ftp.nluug.nl/pub/gnu/hello/hello-2.1.1.tar.gz; # path
    md5 = "70c9ccf9fac07f762c24f2df2290784d";
  };
  inherit perl; # 6
}
