import math,strutils

proc fixedWidth(input: string, minFieldSize: int):string {.inline.} =
  # Note that field size is a minimum- will expand field if input
  # string is larger
  if input.startsWith("-"):
    return(input & repeatchar(count=(abs(minFieldSize-len(input))),c=' '))
  else:
    return(" " & input & repeatchar(count=(abs(minFieldSize-len(input))-1),c=' '))

template mathOnInterval(lowbound,highbound:float,counts: int,p:proc) =
  block:
    var step:    float = (highbound - lowbound)/(max(counts,1))
    var current: float = lowbound
    while current < highbound:
      echo($fixedWidth($current,25) & ": " & $fixedWidth($p(current),25))
      current += step

echo "Sine of theta from 0 to 2*PI by PI/12"
mathOnInterval(0.0,2.0*PI,12,sin)
echo("\n")
echo "Cosine of theta from 0 to 2*PI by PI/12"
mathOnInterval(0.0,2.0*PI,12,cos)

# The first example above is much the same as:
# for i in 1..100:
#   echo($sin( (float(i)/100.0) * 2.0*PI ))
