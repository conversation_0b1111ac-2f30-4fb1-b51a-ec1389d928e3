FORMAT: 1A
HOST: http://polls.apiblueprint.org/

# Polls

Polls is a simple API allowing consumers to view polls and vote in them.

# Polls API Root [/]

## Group Question

Resources related to questions in the API.

## Question [/questions/{question_id}]

+ Parameters
    + question_id: 1 (number, required) - ID of the Question in form of an integer

+ Attributes
    + question: `Favourite programming language?` (required)
    + published_at: `2014-11-11T08:40:51.620Z` - An ISO8601 date when the question was published
    + choices (array[Choice], required) - An array of Choice objects
    + url: /questions/1

### View a Questions Detail [GET]

+ Response 200 (application/json)
    + Attributes (Question)

### Delete a Question [DELETE]

+ Relation: delete
+ Response 204
