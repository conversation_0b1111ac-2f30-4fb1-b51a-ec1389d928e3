/**
 * Verilog Lexer
 */
module Foo(
  input logic Clk_CI,
  input logic Rst_RBI,
  input logic A,
  input logic B,
  output logic C
);
  logic C_DN, C_DP;

  assign C = C_DP;

  always_comb begin : proc_next_state
    C_DN = A + B;
  end

  // Clocked process
  always_ff @(posedge Clk_CI, negedge Rst_RBI) begin
    if(~Rst_RBI) begin
      C_DP <= 1'b0;
    end else begin
      C_DP <= C_DN;
    end
  end
endmodule
