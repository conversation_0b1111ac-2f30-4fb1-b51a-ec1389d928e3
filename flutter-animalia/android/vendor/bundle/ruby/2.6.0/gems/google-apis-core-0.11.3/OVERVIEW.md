# Core classes for Google REST Clients

This library includes common base classes and dependencies used by legacy REST
clients for Google APIs. It is used by client libraries, but you should not
need to install it by itself.

## Documentation

More detailed descriptions of the Google legacy REST clients are available in two documents.

 *  The [Usage Guide](https://github.com/googleapis/google-api-ruby-client/blob/main/docs/usage-guide.md) discusses how to make API calls, how to use the provided data structures, and how to work the various features of the client library, including media upload and download, error handling, retries, pagination, and logging.
 *  The [Auth Guide](https://github.com/googleapis/google-api-ruby-client/blob/main/docs/auth-guide.md) discusses authentication in the client libraries, including API keys, OAuth 2.0, service accounts, and environment variables.

For reference information on specific calls in the clients, see the {Google::Apis class reference docs}.

## License

This library is licensed under Apache 2.0. Full license text is available in the {file:LICENSE.md LICENSE}.

## Support

Please [report bugs at the project on Github](https://github.com/google/google-api-ruby-client/issues). Don't hesitate to [ask questions](http://stackoverflow.com/questions/tagged/google-api-ruby-client) about the client or APIs on [StackOverflow](http://stackoverflow.com).
