## 0.0.7  Tue Sep  1 20:25:00 PDT 2020

- Avoid loading Faraday by assuming Faraday registry API (chesterbr)
- To support change above, require Faraday 0.8 as a minimum (chesterbr)

## 0.0.6  <PERSON>e Jan 21 16:34:35 PST 2014

- Support Faraday 0.9 registry API (cameron-martin)
- Support specifying CookieJar in configuration (cameron-martin)

## 0.0.5  Mon Jan 20 21:53:13 PST 2014

- Lock faraday dependency to < 0.9.0 for now
- Ability to add extra cookies in addition to the ones in <PERSON><PERSON> (nanjingboy)

## 0.0.4  Mon Aug 12 23:11:46 PDT 2013

- Fix a crash when there's no valid response header #3

## 0.0.3  Sun Apr 21 08:14:32 CST 2013

- Switch to http-cookie gem (knu) #2

## 0.0.2

- Repackaging
- Typo in README (tmaesaka) #1

## 0.0.1

- Initial release
