Build settings from command line:
    SDKROOT = iphonesimulator7.0

=== BUILD TARGET Pods-ObjectiveSugarTests-Kiwi OF PROJECT Pods WITH CONFIGURATION Debug ===

Check dependencies

=== BUILD TARGET Pods-ObjectiveSugar OF PROJECT Pods WITH CONFIGURATION Debug ===

Check dependencies

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/ObjectiveSugar-ayzdhqmmwtqgysdpznmovjlupqjy/Build/Intermediates/Pods.build/Debug-iphonesimulator/Pods-ObjectiveSugar.build/Objects-normal/i386/NSNumber+ObjectiveSugar.o /Users/<USER>/code/OSS/ObjectiveSugar/Classes/NSNumber+ObjectiveSugar.m normal i386 objective-c com.apple.compilers.llvm.clang.1_0.compiler
    cd /Users/<USER>/code/OSS/ObjectiveSugar/Example/Pods
    setenv LANG en_US.US-ASCII
    setenv PATH "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Users/<USER>/code/go/bin:/Users/<USER>/.rbenv/shims:/Users/<USER>/.rbenv/bin:/usr/local/share/npm/bin:/usr/local/bin:/Library/Python/2.7/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -arch i386 -fmessage-length=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit=0 -std=gnu99 -fmodules -fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache -Wno-trigraphs -fpascal-strings -O0 -Wno-missing-field-initializers -Wno-missing-prototypes -Werror=return-type -Wno-implicit-atomic-properties -Werror=deprecated-objc-isa-usage -Werror=objc-root-class -Wno-receiver-is-weak -Wno-arc-repeated-use-of-weak -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wno-deprecated-implementations -DDEBUG=1 -DCOCOAPODS=1 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator7.0.sdk -fexceptions -fasm-blocks -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -g -Wno-sign-conversion -fobjc-abi-version=2 -fobjc-legacy-dispatch -mios-simulator-version-min=5.0 -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ObjectiveSugar-ayzdhqmmwtqgysdpznmovjlupqjy/Build/Intermediates/Pods.build/Debug-iphonesimulator/Pods-ObjectiveSugar.build/Pods-ObjectiveSugar-generated-files.hmap -I/Users/<USER>/Library/Developer/Xcode/DerivedData/ObjectiveSugar-ayzdhqmmwtqgysdpznmovjlupqjy/Build/Intermediates/Pods.build/Debug-iphonesimulator/Pods-ObjectiveSugar.build/Pods-ObjectiveSugar-own-target-headers.hmap -I/Users/<USER>/Library/Developer/Xcode/DerivedData/ObjectiveSugar-ayzdhqmmwtqgysdpznmovjlupqjy/Build/Intermediates/Pods.build/Debug-iphonesimulator/Pods-ObjectiveSugar.build/Pods-ObjectiveSugar-all-target-headers.hmap -iquote /Users/<USER>/Library/Developer/Xcode/DerivedData/ObjectiveSugar-ayzdhqmmwtqgysdpznmovjlupqjy/Build/Intermediates/Pods.build/Debug-iphonesimulator/Pods-ObjectiveSugar.build/Pods-ObjectiveSugar-project-headers.hmap -I/Users/<USER>/Library/Developer/Xcode/DerivedData/ObjectiveSugar-ayzdhqmmwtqgysdpznmovjlupqjy/Build/Products/Debug-iphonesimulator/include -I/Users/<USER>/code/OSS/ObjectiveSugar/Example/Pods/BuildHeaders -I/Users/<USER>/code/OSS/ObjectiveSugar/Example/Pods/BuildHeaders/ObjectiveSugar -I/Users/<USER>/code/OSS/ObjectiveSugar/Example/Pods/Headers -I/Users/<USER>/code/OSS/ObjectiveSugar/Example/Pods/Headers/Kiwi -I/Users/<USER>/code/OSS/ObjectiveSugar/Example/Pods/Headers/ObjectiveSugar -I/Users/<USER>/Library/Developer/Xcode/DerivedData/ObjectiveSugar-ayzdhqmmwtqgysdpznmovjlupqjy/Build/Intermediates/Pods.build/Debug-iphonesimulator/Pods-ObjectiveSugar.build/DerivedSources/i386 -I/Users/<USER>/Library/Developer/Xcode/DerivedData/ObjectiveSugar-ayzdhqmmwtqgysdpznmovjlupqjy/Build/Intermediates/Pods.build/Debug-iphonesimulator/Pods-ObjectiveSugar.build/DerivedSources -F/Users/<USER>/Library/Developer/Xcode/DerivedData/ObjectiveSugar-ayzdhqmmwtqgysdpznmovjlupqjy/Build/Products/Debug-iphonesimulator -include /Users/<USER>/Library/Developer/Xcode/DerivedData/ObjectiveSugar-ayzdhqmmwtqgysdpznmovjlupqjy/Build/Intermediates/PrecompiledHeaders/Pods-ObjectiveSugar-prefix-fbehxvikzshezadcwuseekuhbnus/Pods-ObjectiveSugar-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/ObjectiveSugar-ayzdhqmmwtqgysdpznmovjlupqjy/Build/Intermediates/Pods.build/Debug-iphonesimulator/Pods-ObjectiveSugar.build/Objects-normal/i386/NSNumber+ObjectiveSugar.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/ObjectiveSugar-ayzdhqmmwtqgysdpznmovjlupqjy/Build/Intermediates/Pods.build/Debug-iphonesimulator/Pods-ObjectiveSugar.build/Objects-normal/i386/NSNumber+ObjectiveSugar.dia -c /Users/<USER>/code/OSS/ObjectiveSugar/Classes/NSNumber+ObjectiveSugar.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/ObjectiveSugar-ayzdhqmmwtqgysdpznmovjlupqjy/Build/Intermediates/Pods.build/Debug-iphonesimulator/Pods-ObjectiveSugar.build/Objects-normal/i386/NSNumber+ObjectiveSugar.o
/Users/<USER>/code/OSS/ObjectiveSugar/Classes/NSNumber+ObjectiveSugar.m:26:5: error: use of undeclared identifier 'trololo'
    trololo
    ^
/Users/<USER>/code/OSS/ObjectiveSugar/Classes/NSNumber+ObjectiveSugar.m:47:12: error: returning 'float' from a function with incompatible result type 'NSNumber *'
    return (self.floatValue * 60);
           ^~~~~~~~~~~~~~~~~~~~~~
2 errors generated.

