# WARNING ABOUT GENERATED CODE
#
# This file is generated. See the contributing guide for more information:
# https://github.com/aws/aws-sdk-ruby/blob/version-3/CONTRIBUTING.md
#
# WARNING ABOUT GENERATED CODE

module Aws
  module S3
    # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLogging.html
    class BucketLogging
      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLogging.html#initialize-instance_method
      def initialize: (String bucket_name, Hash[Symbol, untyped] options) -> void
                    | (bucket_name: String, ?client: Client) -> void
                    | (Hash[Symbol, untyped] args) -> void

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLogging.html#bucket_name-instance_method
      def bucket_name: () -> String

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLogging.html#logging_enabled-instance_method
      def logging_enabled: () -> Types::LoggingEnabled

      def client: () -> Client

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLogging.html#load-instance_method
      def load: () -> self
      alias reload load

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLogging.html#data-instance_method
      def data: () -> Types::GetBucketLoggingOutput

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLogging.html#data_loaded?-instance_method
      def data_loaded?: () -> bool


      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLogging.html#put-instance_method
      def put: (
                 bucket_logging_status: {
                   logging_enabled: {
                     target_bucket: ::String,
                     target_grants: Array[
                       {
                         grantee: {
                           display_name: ::String?,
                           email_address: ::String?,
                           id: ::String?,
                           type: ("CanonicalUser" | "AmazonCustomerByEmail" | "Group"),
                           uri: ::String?
                         }?,
                         permission: ("FULL_CONTROL" | "READ" | "WRITE")?
                       },
                     ]?,
                     target_prefix: ::String,
                     target_object_key_format: {
                       simple_prefix: {
                       }?,
                       partitioned_prefix: {
                         partition_date_source: ("EventTime" | "DeliveryTime")?
                       }?
                     }?
                   }?
                 },
                 ?content_md5: ::String,
                 ?checksum_algorithm: ("CRC32" | "CRC32C" | "SHA1" | "SHA256" | "CRC64NVME"),
                 ?expected_bucket_owner: ::String
               ) -> ::Aws::EmptyStructure
             | (?Hash[Symbol, untyped]) -> ::Aws::EmptyStructure

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLogging.html#bucket-instance_method
      def bucket: () -> Bucket

      class Collection < ::Aws::Resources::Collection[BucketLogging]
      end
    end
  end
end
