# WARNING ABOUT GENERATED CODE
#
# This file is generated. See the contributing guide for more information:
# https://github.com/aws/aws-sdk-ruby/blob/version-3/CONTRIBUTING.md
#
# WARNING ABOUT GENERATED CODE

module Aws
  module S3
    # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLifecycle.html
    class BucketLifecycle
      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLifecycle.html#initialize-instance_method
      def initialize: (String bucket_name, Hash[Symbol, untyped] options) -> void
                    | (bucket_name: String, ?client: Client) -> void
                    | (Hash[Symbol, untyped] args) -> void

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLifecycle.html#bucket_name-instance_method
      def bucket_name: () -> String

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLifecycle.html#rules-instance_method
      def rules: () -> ::Array[Types::Rule]

      def client: () -> Client

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLifecycle.html#load-instance_method
      def load: () -> self
      alias reload load

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLifecycle.html#data-instance_method
      def data: () -> Types::GetBucketLifecycleOutput

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLifecycle.html#data_loaded?-instance_method
      def data_loaded?: () -> bool


      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLifecycle.html#delete-instance_method
      def delete: (
                    ?expected_bucket_owner: ::String
                  ) -> ::Aws::EmptyStructure
                | (?Hash[Symbol, untyped]) -> ::Aws::EmptyStructure

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLifecycle.html#put-instance_method
      def put: (
                 ?content_md5: ::String,
                 ?checksum_algorithm: ("CRC32" | "CRC32C" | "SHA1" | "SHA256" | "CRC64NVME"),
                 ?lifecycle_configuration: {
                   rules: Array[
                     {
                       expiration: {
                         date: ::Time?,
                         days: ::Integer?,
                         expired_object_delete_marker: bool?
                       }?,
                       id: ::String?,
                       prefix: ::String,
                       status: ("Enabled" | "Disabled"),
                       transition: {
                         date: ::Time?,
                         days: ::Integer?,
                         storage_class: ("GLACIER" | "STANDARD_IA" | "ONEZONE_IA" | "INTELLIGENT_TIERING" | "DEEP_ARCHIVE" | "GLACIER_IR")?
                       }?,
                       noncurrent_version_transition: {
                         noncurrent_days: ::Integer?,
                         storage_class: ("GLACIER" | "STANDARD_IA" | "ONEZONE_IA" | "INTELLIGENT_TIERING" | "DEEP_ARCHIVE" | "GLACIER_IR")?,
                         newer_noncurrent_versions: ::Integer?
                       }?,
                       noncurrent_version_expiration: {
                         noncurrent_days: ::Integer?,
                         newer_noncurrent_versions: ::Integer?
                       }?,
                       abort_incomplete_multipart_upload: {
                         days_after_initiation: ::Integer?
                       }?
                     },
                   ]
                 },
                 ?expected_bucket_owner: ::String
               ) -> ::Aws::EmptyStructure
             | (?Hash[Symbol, untyped]) -> ::Aws::EmptyStructure

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketLifecycle.html#bucket-instance_method
      def bucket: () -> Bucket

      class Collection < ::Aws::Resources::Collection[BucketLifecycle]
      end
    end
  end
end
