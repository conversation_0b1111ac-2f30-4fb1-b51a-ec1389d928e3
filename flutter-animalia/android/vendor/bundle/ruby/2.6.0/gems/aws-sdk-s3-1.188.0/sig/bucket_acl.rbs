# WARNING ABOUT GENERATED CODE
#
# This file is generated. See the contributing guide for more information:
# https://github.com/aws/aws-sdk-ruby/blob/version-3/CONTRIBUTING.md
#
# WARNING ABOUT GENERATED CODE

module Aws
  module S3
    # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketAcl.html
    class BucketAcl
      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketAcl.html#initialize-instance_method
      def initialize: (String bucket_name, Hash[Symbol, untyped] options) -> void
                    | (bucket_name: String, ?client: Client) -> void
                    | (Hash[Symbol, untyped] args) -> void

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketAcl.html#bucket_name-instance_method
      def bucket_name: () -> String

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketAcl.html#owner-instance_method
      def owner: () -> Types::Owner

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketAcl.html#grants-instance_method
      def grants: () -> ::Array[Types::Grant]

      def client: () -> Client

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketAcl.html#load-instance_method
      def load: () -> self
      alias reload load

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketAcl.html#data-instance_method
      def data: () -> Types::GetBucketAclOutput

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketAcl.html#data_loaded?-instance_method
      def data_loaded?: () -> bool


      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketAcl.html#put-instance_method
      def put: (
                 ?acl: ("private" | "public-read" | "public-read-write" | "authenticated-read"),
                 ?access_control_policy: {
                   grants: Array[
                     {
                       grantee: {
                         display_name: ::String?,
                         email_address: ::String?,
                         id: ::String?,
                         type: ("CanonicalUser" | "AmazonCustomerByEmail" | "Group"),
                         uri: ::String?
                       }?,
                       permission: ("FULL_CONTROL" | "WRITE" | "WRITE_ACP" | "READ" | "READ_ACP")?
                     },
                   ]?,
                   owner: {
                     display_name: ::String?,
                     id: ::String?
                   }?
                 },
                 ?content_md5: ::String,
                 ?checksum_algorithm: ("CRC32" | "CRC32C" | "SHA1" | "SHA256" | "CRC64NVME"),
                 ?grant_full_control: ::String,
                 ?grant_read: ::String,
                 ?grant_read_acp: ::String,
                 ?grant_write: ::String,
                 ?grant_write_acp: ::String,
                 ?expected_bucket_owner: ::String
               ) -> ::Aws::EmptyStructure
             | (?Hash[Symbol, untyped]) -> ::Aws::EmptyStructure

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/BucketAcl.html#bucket-instance_method
      def bucket: () -> Bucket

      class Collection < ::Aws::Resources::Collection[BucketAcl]
      end
    end
  end
end
