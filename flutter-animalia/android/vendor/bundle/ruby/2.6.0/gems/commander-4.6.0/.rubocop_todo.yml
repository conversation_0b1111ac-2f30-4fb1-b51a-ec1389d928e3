# This configuration was generated by `rubocop --auto-gen-config`
# on 2015-02-16 16:08:54 -0800 using RuboCop version 0.29.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 2
Security/Eval:
  Enabled: false

# Offense count: 2
Lint/SuppressedException:
  Enabled: false

# Offense count: 5
Metrics/AbcSize:
  Max: 30

# Offense count: 1
# Configuration parameters: CountComments.
Metrics/ClassLength:
  Enabled: false

# Offense count: 4
Metrics/CyclomaticComplexity:
  Max: 13

# Offense count: 89
# Configuration parameters: AllowURI, URISchemes.
Layout/LineLength:
  Max: 242

# Offense count: 7
# Configuration parameters: CountComments.
Metrics/MethodLength:
  Max: 36

# Offense count: 4
Metrics/PerceivedComplexity:
  Max: 14

# Offense count: 1
Naming/AccessorMethodName:
  Enabled: false

# Offense count: 18
Style/Documentation:
  Enabled: false

# Offense count: 12
# Configuration parameters: AllowedVariables.
Style/GlobalVars:
  Enabled: false

# Offense count: 1
# Configuration parameters: MaxLineLength.
Style/IfUnlessModifier:
  Enabled: false

# Offense count: 1
Style/MultilineBlockChain:
  Enabled: false

# Offense count: 1
Style/MultilineTernaryOperator:
  Enabled: false

# Offense count: 5
Style/RescueModifier:
  Enabled: false

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: ExactNameMatch, AllowPredicates, AllowDSLWriters, Whitelist.
Style/TrivialAccessors:
  Enabled: false
