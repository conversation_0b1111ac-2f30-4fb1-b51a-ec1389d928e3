  <%= HighLine.default_instance.color "NAME", :bold %>:

    <%= program :name %>

  <%= HighLine.default_instance.color "DESCRIPTION", :bold %>:

    <%= Commander::HelpFormatter.indent 4, program(:description) %>

  <%= HighLine.default_instance.color "COMMANDS", :bold %>:
<% for name, command in @commands.sort -%>
	<% unless alias? name %>
    <%= "%-#{max_command_length}s %s" % [command.name, command.summary || command.description] -%>
	<% end -%>
<% end %>
<% unless @aliases.empty? %>
  <%= HighLine.default_instance.color "ALIASES", :bold %>:
  <% for alias_name, args in @aliases.sort %>
    <%= "%-#{max_aliases_length}s %s %s" % [alias_name, command(alias_name).name, args.join(' ')] -%>
  <% end %>
<% end %>
<% unless @options.empty? -%>
  <%= HighLine.default_instance.color "GLOBAL OPTIONS", :bold %>:
	<% for option in @options -%>

    <%= option[:switches].join ', ' %>
        <%= option[:description] %>
	<% end -%>
<% end -%>
<% if program :help -%>
  <% for title, body in program(:help) %>
  <%= HighLine.default_instance.color title.to_s.upcase, :bold %>:

    <%= body %>
  <% end -%>
<% end -%>

