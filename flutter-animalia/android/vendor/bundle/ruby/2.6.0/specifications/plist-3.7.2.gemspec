# -*- encoding: utf-8 -*-
# stub: plist 3.7.2 ruby lib

Gem::Specification.new do |s|
  s.name = "plist".freeze
  s.version = "3.7.2"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON> Bleything".freeze, "<PERSON>".freeze]
  s.date = "2024-12-26"
  s.description = "Plist is a library to manipulate Property List files, also known as plists. It can parse plist files into native Ruby data structures as well as generating new plist files from your Ruby objects.".freeze
  s.homepage = "https://github.com/patsplat/plist".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 1.9.3".freeze)
  s.rubygems_version = "*******".freeze
  s.summary = "All-purpose Property List manipulation library".freeze

  s.installed_by_version = "*******" if s.respond_to? :installed_by_version
end
