# -*- encoding: utf-8 -*-
# stub: aws-eventstream 1.3.2 ruby lib

Gem::Specification.new do |s|
  s.name = "aws-eventstream".freeze
  s.version = "1.3.2"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "changelog_uri" => "https://github.com/aws/aws-sdk-ruby/tree/version-3/gems/aws-eventstream/CHANGELOG.md", "source_code_uri" => "https://github.com/aws/aws-sdk-ruby/tree/version-3/gems/aws-eventstream" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["Amazon Web Services".freeze]
  s.date = "2025-03-06"
  s.description = "Amazon Web Services event stream library. Decodes and encodes binary stream under `vnd.amazon.event-stream` content-type".freeze
  s.homepage = "https://github.com/aws/aws-sdk-ruby".freeze
  s.licenses = ["Apache-2.0".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.5".freeze)
  s.rubygems_version = "*******".freeze
  s.summary = "AWS Event Stream Library".freeze

  s.installed_by_version = "*******" if s.respond_to? :installed_by_version
end
