<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Permissions for Google Maps and location -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- Permission for contacts -->
    <uses-permission android:name="android.permission.READ_CONTACTS" />

    <application
        android:label="animaliaproject"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <!-- Google Maps API Key -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyCO77ldStnRCjfZ3EThONj8F8X6d3EVWvI" />

        <!-- Firebase Auth Play Integrity Bypass Configuration -->
        <meta-data
            android:name="firebase_auth_play_integrity_debug_bypass"
            android:value="true" />

        <!-- Disable Play Integrity completely -->
        <meta-data
            android:name="firebase_auth_disable_play_integrity"
            android:value="true" />

        <!-- Force reCAPTCHA for phone auth -->
        <meta-data
            android:name="firebase_auth_force_recaptcha"
            android:value="true" />

        <!-- Disable app verification for testing -->
        <meta-data
            android:name="firebase_auth_app_verification_disabled_for_testing"
            android:value="true" />

        <!-- Firebase Auth testing mode -->
        <meta-data
            android:name="firebase_auth_testing_mode"
            android:value="true" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>
</manifest>
