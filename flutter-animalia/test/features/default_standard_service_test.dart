import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:animaliaproject/widgets/new_appointment/appointment_form_data.dart';

void main() {
  group('Feature 3: Default Standard Service', () {
    test('should auto-select "Serviciu Standard" when available', () {
      // Arrange
      final formData = AppointmentFormData(
        appointmentDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        services: [], // No services selected initially
        availableServices: [
          'Baie completă',
          'Serviciu Standard',
          'Tuns și aranjat',
        ],
      );

      // Act - Simulate the auto-selection logic from _loadServices
      if (formData.services.isEmpty && formData.availableServices.isNotEmpty) {
        const defaultServiceName = 'Serviciu Standard';
        if (formData.availableServices.contains(defaultServiceName)) {
          formData.services = [defaultServiceName];
        }
      }

      // Assert
      expect(formData.services, ['Serviciu Standard']);
    });

    test('should auto-select first service when "Serviciu Standard" is not available', () {
      // Arrange
      final formData = AppointmentFormData(
        appointmentDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        services: [], // No services selected initially
        availableServices: [
          'Baie completă',
          'Tuns și aranjat',
          'Toaletare completă',
        ],
      );

      // Act - Simulate the fallback logic from _loadServices
      if (formData.services.isEmpty && formData.availableServices.isNotEmpty) {
        const defaultServiceName = 'Serviciu Standard';
        if (formData.availableServices.contains(defaultServiceName)) {
          formData.services = [defaultServiceName];
        } else {
          // Fallback: select the first available service
          formData.services = [formData.availableServices.first];
        }
      }

      // Assert
      expect(formData.services, ['Baie completă']);
    });

    test('should not auto-select when services are already selected', () {
      // Arrange
      final formData = AppointmentFormData(
        appointmentDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        services: ['Tuns și aranjat'], // Service already selected
        availableServices: [
          'Baie completă',
          'Serviciu Standard',
          'Tuns și aranjat',
        ],
      );

      // Act - Simulate the auto-selection logic from _loadServices
      if (formData.services.isEmpty && formData.availableServices.isNotEmpty) {
        const defaultServiceName = 'Serviciu Standard';
        if (formData.availableServices.contains(defaultServiceName)) {
          formData.services = [defaultServiceName];
        }
      }

      // Assert - Should remain unchanged
      expect(formData.services, ['Tuns și aranjat']);
    });

    test('should not auto-select when no services are available', () {
      // Arrange
      final formData = AppointmentFormData(
        appointmentDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        services: [], // No services selected initially
        availableServices: [], // No services available
      );

      // Act - Simulate the auto-selection logic from _loadServices
      if (formData.services.isEmpty && formData.availableServices.isNotEmpty) {
        const defaultServiceName = 'Serviciu Standard';
        if (formData.availableServices.contains(defaultServiceName)) {
          formData.services = [defaultServiceName];
        }
      }

      // Assert - Should remain empty
      expect(formData.services, isEmpty);
    });

    test('should verify default service specifications', () {
      // Test the expected specifications for the default service
      const expectedName = 'Serviciu Standard';
      const expectedDuration = 60; // 1 hour in minutes
      const expectedPrice = 200.0; // 200 RON
      const expectedDescription = 'Serviciu standard de toaletare pentru animale';

      // Assert specifications match Feature 3 requirements
      expect(expectedName, 'Serviciu Standard');
      expect(expectedDuration, 60);
      expect(expectedPrice, 200.0);
      expect(expectedDescription, 'Serviciu standard de toaletare pentru animale');
    });

    test('should handle service name case sensitivity', () {
      // Arrange
      final formData = AppointmentFormData(
        appointmentDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        services: [],
        availableServices: [
          'serviciu standard', // lowercase
          'SERVICIU STANDARD', // uppercase
          'Serviciu Standard', // correct case
        ],
      );

      // Act - Test exact match (case sensitive)
      if (formData.services.isEmpty && formData.availableServices.isNotEmpty) {
        const defaultServiceName = 'Serviciu Standard';
        if (formData.availableServices.contains(defaultServiceName)) {
          formData.services = [defaultServiceName];
        } else {
          formData.services = [formData.availableServices.first];
        }
      }

      // Assert - Should select the exact match
      expect(formData.services, ['Serviciu Standard']);
    });

    test('should prioritize default service over display order', () {
      // Arrange - Services in different order, but default should be selected
      final formData = AppointmentFormData(
        appointmentDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        services: [],
        availableServices: [
          'Baie completă',
          'Tuns și aranjat',
          'Serviciu Standard', // Not first, but should be selected
          'Toaletare completă',
        ],
      );

      // Act
      if (formData.services.isEmpty && formData.availableServices.isNotEmpty) {
        const defaultServiceName = 'Serviciu Standard';
        if (formData.availableServices.contains(defaultServiceName)) {
          formData.services = [defaultServiceName];
        } else {
          formData.services = [formData.availableServices.first];
        }
      }

      // Assert - Should select default service, not first in list
      expect(formData.services, ['Serviciu Standard']);
    });
  });
}
