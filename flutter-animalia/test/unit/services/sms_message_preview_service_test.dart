import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/services/sms_message_preview_service.dart';

void main() {
  group('SmsMessagePreviewService', () {
    test('should build appointment scheduled message with full info', () {
      final message = SmsMessagePreviewService.buildAppointmentScheduledMessage(
        salonName: 'Animalia Salon',
        clientName: '<PERSON>',
        petName: 'Rex',
        salonAddress: 'Str. Florilor nr. 15, <PERSON><PERSON><PERSON><PERSON>',
        salonPhone: '0721 123 456',
        appointmentDate: DateTime(2024, 12, 25),
        startTime: DateTime(2024, 12, 25, 14, 30),
      );

      expect(message, contains('De la Animalia Salon'));
      expect(message, contains('<PERSON><PERSON><PERSON>, <PERSON>!'));
      expect(message, contains('pentru Rex'));
      expect(message, contains('25.12.2024'));
      expect(message, contains('14:30'));
      expect(message, contains('Str. Florilor nr. 15, <PERSON><PERSON><PERSON><PERSON>'));
      expect(message, contains('0721 123 456'));
    });

    test('should build appointment scheduled message without pet name', () {
      final message = SmsMessagePreviewService.buildAppointmentScheduledMessage(
        salonName: 'Animalia Salon',
        clientName: 'Maria Popescu',
        petName: null, // No pet name
        salonAddress: 'Str. Florilor nr. 15, București',
        salonPhone: '0721 123 456',
        appointmentDate: DateTime(2024, 12, 25),
        startTime: DateTime(2024, 12, 25, 14, 30),
      );

      expect(message, contains('De la Animalia Salon'));
      expect(message, contains('Bună, Maria Popescu!'));
      expect(message, contains('Programarea dumneavoastră'));
      expect(message, isNot(contains('pentru Rex')));
      expect(message, contains('25.12.2024'));
      expect(message, contains('14:30'));
    });

    test('should build minimal appointment scheduled message', () {
      final message = SmsMessagePreviewService.buildAppointmentScheduledMessage(
        salonName: null,
        clientName: null,
        petName: null,
        salonAddress: 'Str. Florilor nr. 15, București',
        salonPhone: '0721 123 456',
        appointmentDate: DateTime(2024, 12, 25),
        startTime: DateTime(2024, 12, 25, 14, 30),
      );

      expect(message, contains('Programarea dumneavoastră este confirmată'));
      expect(message, contains('25.12.2024'));
      expect(message, contains('14:30'));
      expect(message, contains('Str. Florilor nr. 15, București'));
    });

    test('should build appointment cancelled message', () {
      final message = SmsMessagePreviewService.buildAppointmentCancelledMessage(
        salonName: 'Animalia Salon',
        clientName: 'Maria Popescu',
        petName: 'Rex',
        salonAddress: 'Str. Florilor nr. 15, București',
        salonPhone: '0721 123 456',
        appointmentDate: DateTime(2024, 12, 25),
      );

      expect(message, contains('De la Animalia Salon'));
      expect(message, contains('Bună, Maria Popescu!'));
      expect(message, contains('pentru Rex'));
      expect(message, contains('25.12.2024'));
      expect(message, contains('a fost anulată'));
      expect(message, contains('0721 123 456'));
    });

    test('should build appointment rescheduled message', () {
      final message = SmsMessagePreviewService.buildAppointmentRescheduledMessage(
        salonName: 'Animalia Salon',
        clientName: 'Maria Popescu',
        petName: 'Rex',
        salonAddress: 'Str. Florilor nr. 15, București',
        salonPhone: '0721 123 456',
        oldDate: DateTime(2024, 12, 20),
        newDate: DateTime(2024, 12, 25),
        newStartTime: DateTime(2024, 12, 25, 15, 0),
      );

      expect(message, contains('De la Animalia Salon'));
      expect(message, contains('Bună, Maria Popescu!'));
      expect(message, contains('pentru Rex'));
      expect(message, contains('reprogramată'));
      expect(message, contains('20.12.2024'));
      expect(message, contains('25.12.2024'));
      expect(message, contains('15:00'));
    });

    test('should build day before reminder message', () {
      final message = SmsMessagePreviewService.buildDayBeforeReminderMessage(
        salonName: 'Animalia Salon',
        clientName: 'Maria Popescu',
        petName: 'Rex',
        salonAddress: 'Str. Florilor nr. 15, București',
        salonPhone: '0721 123 456',
        appointmentDate: DateTime(2024, 12, 25),
        startTime: DateTime(2024, 12, 25, 14, 30),
      );

      expect(message, contains('De la Animalia Salon'));
      expect(message, contains('Bună, Maria Popescu!'));
      expect(message, contains('mâine'));
      expect(message, contains('Rex'));
      expect(message, contains('25.12.2024'));
      expect(message, contains('14:30'));
    });

    test('should build six hours before reminder message', () {
      final message = SmsMessagePreviewService.buildSixHoursBeforeReminderMessage(
        salonName: 'Animalia Salon',
        clientName: 'Maria Popescu',
        petName: 'Rex',
        salonAddress: 'Str. Florilor nr. 15, București',
        salonPhone: '0721 123 456',
        appointmentDate: DateTime(2024, 12, 25),
        startTime: DateTime(2024, 12, 25, 14, 30),
      );

      expect(message, contains('De la Animalia Salon'));
      expect(message, contains('Bună, Maria Popescu!'));
      expect(message, contains('În câteva ore'));
      expect(message, contains('Rex'));
      expect(message, contains('14:30'));
    });

    test('should build follow-up message', () {
      final message = SmsMessagePreviewService.buildFollowUpMessage(
        salonName: 'Animalia Salon',
        clientName: 'Maria Popescu',
        petName: 'Rex',
        salonPhone: '0721 123 456',
      );

      expect(message, contains('De la Animalia Salon'));
      expect(message, contains('Bună, Maria Popescu!'));
      expect(message, contains('Rex'));
      expect(message, contains('s-a simțit bine'));
      expect(message, contains('0721 123 456'));
    });

    test('should provide sample data', () {
      final sampleData = SmsMessagePreviewService.getSampleData();

      expect(sampleData['salonName'], isA<String>());
      expect(sampleData['clientName'], isA<String>());
      expect(sampleData['petName'], isA<String>());
      expect(sampleData['salonAddress'], isA<String>());
      expect(sampleData['salonPhone'], isA<String>());
      expect(sampleData['appointmentDate'], isA<DateTime>());
      expect(sampleData['startTime'], isA<DateTime>());
    });

    test('should use default values when data is missing', () {
      final message = SmsMessagePreviewService.buildAppointmentCancelledMessage(
        salonName: null,
        clientName: null,
        petName: null,
        salonAddress: null,
        salonPhone: null,
        appointmentDate: DateTime(2024, 12, 25),
      );

      expect(message, contains('Salonul nostru'));
      expect(message, contains('Stimate client'));
      expect(message, contains('animalul dumneavoastră'));
      expect(message, contains('adresa salonului'));
      expect(message, contains('salonul nostru'));
    });
  });
}
