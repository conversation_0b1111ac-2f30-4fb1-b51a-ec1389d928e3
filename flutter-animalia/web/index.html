<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Animalia Grooming - Professional Pet Grooming Services in Romania">

  <!-- Google Sign-In configuration -->
  <meta name="google-signin-client_id" content="166674682070-hfhkp61b4itaqs5t332kb4seg2u34o01.apps.googleusercontent.com">

  <!-- Apple Sign-In configuration for web -->
  <meta name="appleid-signin-client-id" content="ro.animalia-programari.animalia.web">
  <meta name="appleid-signin-scope" content="name email">
  <meta name="appleid-signin-redirect-uri" content="https://animalia-de0f1.firebaseapp.com">
  <meta name="appleid-signin-state" content="signin">
  <meta name="appleid-signin-use-popup" content="true">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Animalia Programari">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Animalia Programari - Professional Pet Grooming</title>
  <link rel="manifest" href="manifest.json">
</head>
<body>
  <!-- Loading indicator -->
  <div id="loading" style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: Arial, sans-serif;">
    <div style="text-align: center;">
      <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
      <p>Loading Animalia Grooming...</p>
    </div>
  </div>

  <style>
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>


  <!-- RevenueCat Web SDK -->
  <script src="https://js.revenuecat.com/v1/revenuecat.js"></script>

  <!-- Google Maps JavaScript API -->
  <script src="google_maps_config.js"></script>
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCO77ldStnRCjfZ3EThONj8F8X6d3EVWvI&libraries=places"></script>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
