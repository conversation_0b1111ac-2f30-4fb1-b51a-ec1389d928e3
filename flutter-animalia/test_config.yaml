# Flutter Test Configuration
# This file configures test execution for the Animalia project

# Test runner configuration
test_runner:
  # Timeout for individual tests (in seconds)
  timeout: 30
  
  # Number of concurrent test processes
  concurrency: 4
  
  # Test reporter format
  reporter: expanded
  
  # Coverage configuration
  coverage: true
  
  # Test directories to include
  include_dirs:
    - test/unit/
    - test/widget/
    - test/features/
    - test/services/
    - test/providers/
    - test/integration/
  
  # Test files to exclude
  exclude_patterns:
    - "**/*_manual_test.dart"
    - "**/visual/**"
    - "**/performance/**"
  
  # Environment variables for tests
  environment:
    FLUTTER_TEST: "true"
    CI: "true"
    
# Coverage configuration
coverage:
  # Minimum coverage threshold (percentage)
  threshold: 70
  
  # Files to exclude from coverage
  exclude:
    - "lib/generated/**"
    - "lib/**/*.g.dart"
    - "lib/**/*.freezed.dart"
    - "lib/main.dart"
    - "lib/firebase_options.dart"
    - "test/**"
  
  # Coverage output formats
  formats:
    - lcov
    - html
    - json
    
# Integration test configuration
integration_tests:
  # Device configuration for integration tests
  device:
    platform: android
    api_level: 31
    arch: x86_64
    
  # Test timeout for integration tests (in seconds)
  timeout: 300
  
  # Screenshots configuration
  screenshots:
    enabled: true
    on_failure: true
    directory: "build/screenshots"
    
# Test data configuration
test_data:
  # Mock API responses
  mock_api: true
  
  # Test database configuration
  database:
    type: memory
    reset_between_tests: true
    
  # Authentication mocks
  auth:
    mock_tokens: true
    default_user_id: "test-user-123"
    default_salon_id: "test-salon-456"
    
# Performance test configuration
performance:
  # Enable performance monitoring during tests
  enabled: false
  
  # Performance thresholds
  thresholds:
    widget_build_time_ms: 100
    api_response_time_ms: 1000
    memory_usage_mb: 512
    
# Reporting configuration
reporting:
  # Generate JUnit XML reports
  junit: true
  
  # Generate HTML reports
  html: true
  
  # Upload artifacts
  artifacts:
    - coverage/
    - test-results/
    - screenshots/
    - reports/
    
  # Notification settings
  notifications:
    on_failure: true
    on_success: false
    
# CI/CD specific configuration
ci:
  # Fail fast on first test failure
  fail_fast: true
  
  # Retry failed tests
  retry_count: 2
  
  # Parallel execution
  parallel: true
  
  # Cache configuration
  cache:
    dependencies: true
    build_cache: true
    test_cache: true
