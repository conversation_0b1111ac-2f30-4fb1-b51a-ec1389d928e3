import 'package:flutter/foundation.dart';

import '../../models/api_response.dart';
import '../../models/report_data.dart';
import '../base_service.dart';

/// Service for fetching business reports from the backend
class ReportsService {
  
  /// Get pet distribution report
  static Future<ApiResponse<PetReportData>> getPetReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('📊 Fetching pet report: ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}');
      }

      final response = await BaseService.getSalonResource<Map<String, dynamic>>(
        'reports/pets',
        queryParams: {
          'startDate': startDate.toIso8601String().split('T')[0],
          'endDate': endDate.toIso8601String().split('T')[0],
        },
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final reportData = _parsePetReportData(response.data!);
        
        if (kDebugMode) {
          debugPrint('✅ Pet report fetched successfully: ${reportData.totalPets} pets');
        }
        
        return ApiResponse.success(reportData);
      }

      return ApiResponse<PetReportData>.error(response.error ?? 'Failed to fetch pet report');
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error fetching pet report: $e');
      }
      return ApiResponse<PetReportData>.error('Failed to fetch pet report: $e');
    }
  }

  /// Get service performance report
  static Future<ApiResponse<ServiceReportData>> getServiceReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('📊 Fetching service report: ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}');
      }

      final response = await BaseService.getSalonResource<Map<String, dynamic>>(
        'reports/services',
        queryParams: {
          'startDate': startDate.toIso8601String().split('T')[0],
          'endDate': endDate.toIso8601String().split('T')[0],
        },
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final reportData = _parseServiceReportData(response.data!);
        
        if (kDebugMode) {
          debugPrint('✅ Service report fetched successfully: ${reportData.totalServices} services');
        }
        
        return ApiResponse.success(reportData);
      }

      return ApiResponse<ServiceReportData>.error(response.error ?? 'Failed to fetch service report');
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error fetching service report: $e');
      }
      return ApiResponse<ServiceReportData>.error('Failed to fetch service report: $e');
    }
  }

  /// Get client analytics report
  static Future<ApiResponse<ClientReportData>> getClientReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('📊 Fetching client report: ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}');
      }

      final response = await BaseService.getSalonResource<Map<String, dynamic>>(
        'reports/clients',
        queryParams: {
          'startDate': startDate.toIso8601String().split('T')[0],
          'endDate': endDate.toIso8601String().split('T')[0],
        },
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final reportData = _parseClientReportData(response.data!);
        
        if (kDebugMode) {
          debugPrint('✅ Client report fetched successfully: ${reportData.totalClients} clients');
        }
        
        return ApiResponse.success(reportData);
      }

      return ApiResponse<ClientReportData>.error(response.error ?? 'Failed to fetch client report');
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error fetching client report: $e');
      }
      return ApiResponse<ClientReportData>.error('Failed to fetch client report: $e');
    }
  }

  /// Get staff performance report
  static Future<ApiResponse<StaffPerformanceReportData>> getStaffPerformanceReport({
    required String staffId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('📊 Fetching staff performance report for $staffId: ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}');
      }

      final response = await BaseService.getSalonResource<Map<String, dynamic>>(
        'reports/staff/$staffId/performance',
        queryParams: {
          'startDate': startDate.toIso8601String().split('T')[0],
          'endDate': endDate.toIso8601String().split('T')[0],
        },
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final reportData = _parseStaffPerformanceReportData(response.data!);
        
        if (kDebugMode) {
          debugPrint('✅ Staff performance report fetched successfully: ${reportData.totalAppointments} appointments');
        }
        
        return ApiResponse.success(reportData);
      }

      return ApiResponse<StaffPerformanceReportData>.error(response.error ?? 'Failed to fetch staff performance report');
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error fetching staff performance report: $e');
      }
      return ApiResponse<StaffPerformanceReportData>.error('Failed to fetch staff performance report: $e');
    }
  }

  /// Get revenue analytics report
  static Future<ApiResponse<RevenueReportData>> getRevenueReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('📊 Fetching revenue report: ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}');
      }

      final response = await BaseService.getSalonResource<Map<String, dynamic>>(
        'reports/revenue',
        queryParams: {
          'startDate': startDate.toIso8601String().split('T')[0],
          'endDate': endDate.toIso8601String().split('T')[0],
        },
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final reportData = _parseRevenueReportData(response.data!);
        
        if (kDebugMode) {
          debugPrint('✅ Revenue report fetched successfully: ${reportData.totalRevenue.toStringAsFixed(2)} RON');
        }
        
        return ApiResponse.success(reportData);
      }

      return ApiResponse<RevenueReportData>.error(response.error ?? 'Failed to fetch revenue report');
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error fetching revenue report: $e');
      }
      return ApiResponse<RevenueReportData>.error('Failed to fetch revenue report: $e');
    }
  }

  // Private parsing methods
  static PetReportData _parsePetReportData(Map<String, dynamic> data) {
    return PetReportData(
      title: data['title'] ?? 'Raport Animale',
      startDate: DateTime.parse(data['startDate']),
      endDate: DateTime.parse(data['endDate']),
      sizeBreakdown: (data['sizeBreakdown'] as List)
          .map((item) => BarChartItem(
                label: item['label'],
                value: (item['value'] as num).toDouble(),
                metadata: Map<String, dynamic>.from(item['metadata'] ?? {}),
              ))
          .toList(),
      breedBreakdown: (data['breedBreakdown'] as List)
          .map((item) => BarChartItem(
                label: item['label'],
                value: (item['value'] as num).toDouble(),
                metadata: Map<String, dynamic>.from(item['metadata'] ?? {}),
              ))
          .toList(),
      totalPets: data['totalPets'] ?? 0,
      generatedAt: data['generatedAt'] != null 
          ? DateTime.parse(data['generatedAt'])
          : DateTime.now(),
    );
  }

  static ServiceReportData _parseServiceReportData(Map<String, dynamic> data) {
    return ServiceReportData(
      title: data['title'] ?? 'Raport Servicii',
      startDate: DateTime.parse(data['startDate']),
      endDate: DateTime.parse(data['endDate']),
      serviceRequests: (data['serviceRequests'] as List)
          .map((item) => BarChartItem(
                label: item['label'],
                value: (item['value'] as num).toDouble(),
                metadata: Map<String, dynamic>.from(item['metadata'] ?? {}),
              ))
          .toList(),
      serviceRevenue: (data['serviceRevenue'] as List)
          .map((item) => BarChartItem(
                label: item['label'],
                value: (item['value'] as num).toDouble(),
                metadata: Map<String, dynamic>.from(item['metadata'] ?? {}),
              ))
          .toList(),
      totalServices: data['totalServices'] ?? 0,
      totalRevenue: (data['totalRevenue'] as num?)?.toDouble() ?? 0.0,
      generatedAt: data['generatedAt'] != null 
          ? DateTime.parse(data['generatedAt'])
          : DateTime.now(),
    );
  }

  static ClientReportData _parseClientReportData(Map<String, dynamic> data) {
    return ClientReportData(
      title: data['title'] ?? 'Raport Clienți',
      startDate: DateTime.parse(data['startDate']),
      endDate: DateTime.parse(data['endDate']),
      topClients: (data['topClients'] as List)
          .map((item) => BarChartItem(
                label: item['label'],
                value: (item['value'] as num).toDouble(),
                metadata: Map<String, dynamic>.from(item['metadata'] ?? {}),
              ))
          .toList(),
      totalClients: data['totalClients'] ?? 0,
      averageSpending: (data['averageSpending'] as num?)?.toDouble() ?? 0.0,
      newClients: data['newClients'] ?? 0,
      returningClients: data['returningClients'] ?? 0,
      generatedAt: data['generatedAt'] != null 
          ? DateTime.parse(data['generatedAt'])
          : DateTime.now(),
    );
  }

  static StaffPerformanceReportData _parseStaffPerformanceReportData(Map<String, dynamic> data) {
    return StaffPerformanceReportData(
      title: data['title'] ?? 'Raport Staff',
      startDate: DateTime.parse(data['startDate']),
      endDate: DateTime.parse(data['endDate']),
      staffId: data['staffId'] ?? '',
      staffName: data['staffName'] ?? '',
      totalAppointments: data['totalAppointments'] ?? 0,
      totalRevenue: (data['totalRevenue'] as num?)?.toDouble() ?? 0.0,
      averageRating: (data['averageRating'] as num?)?.toDouble() ?? 0.0,
      completedAppointments: data['completedAppointments'] ?? 0,
      cancelledAppointments: data['cancelledAppointments'] ?? 0,
      utilizationRate: (data['utilizationRate'] as num?)?.toDouble() ?? 0.0,
      serviceBreakdown: (data['serviceBreakdown'] as List)
          .map((item) => BarChartItem(
                label: item['label'],
                value: (item['value'] as num).toDouble(),
                metadata: Map<String, dynamic>.from(item['metadata'] ?? {}),
              ))
          .toList(),
      revenueOverTime: (data['revenueOverTime'] as List)
          .map((item) => LineChartPoint(
                date: DateTime.parse(item['date']),
                value: (item['value'] as num).toDouble(),
                metadata: Map<String, dynamic>.from(item['metadata'] ?? {}),
              ))
          .toList(),
      generatedAt: data['generatedAt'] != null 
          ? DateTime.parse(data['generatedAt'])
          : DateTime.now(),
    );
  }

  static RevenueReportData _parseRevenueReportData(Map<String, dynamic> data) {
    return RevenueReportData(
      title: data['title'] ?? 'Raport Venituri',
      startDate: DateTime.parse(data['startDate']),
      endDate: DateTime.parse(data['endDate']),
      dailyRevenue: (data['dailyRevenue'] as List)
          .map((item) => LineChartPoint(
                date: DateTime.parse(item['date']),
                value: (item['value'] as num).toDouble(),
                metadata: Map<String, dynamic>.from(item['metadata'] ?? {}),
              ))
          .toList(),
      totalRevenue: (data['totalRevenue'] as num?)?.toDouble() ?? 0.0,
      averageDailyRevenue: (data['averageDailyRevenue'] as num?)?.toDouble() ?? 0.0,
      growthRate: (data['growthRate'] as num?)?.toDouble() ?? 0.0,
      revenueByService: (data['revenueByService'] as List)
          .map((item) => BarChartItem(
                label: item['label'],
                value: (item['value'] as num).toDouble(),
                metadata: Map<String, dynamic>.from(item['metadata'] ?? {}),
              ))
          .toList(),
      generatedAt: data['generatedAt'] != null 
          ? DateTime.parse(data['generatedAt'])
          : DateTime.now(),
    );
  }
}
