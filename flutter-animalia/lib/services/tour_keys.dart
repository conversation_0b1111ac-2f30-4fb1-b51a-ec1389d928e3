
import 'package:flutter/cupertino.dart';

class TourKeys {
  // Navigation Tour Keys
  static final welcomeKey = GlobalKey();
  static final navigationBarKey = GlobalKey();
  static final calendarTabKey = GlobalKey();
  static final clientsTabKey = GlobalKey();
  static final notificationsTabKey = GlobalKey();
  static final salonTabKey = GlobalKey();
  static final settingsTabKey = GlobalKey();
  
  /// Get navigation tour keys in order
  static List<GlobalKey> getNavigationTourKeys() => [
    welcomeKey,
    navigationBarKey,
    calendarTabKey,
    clientsTabKey,
    notificationsTabKey,
    salonTabKey,
    settingsTabKey,
  ];
}

/// Settings page tour keys
class SettingsTourKeys {
  static final nameEditKey = GlobalKey();
  static final themeToggleKey = GlobalKey();
  static final salonManagementKey = GlobalKey();
  static final userSettingsKey = GlobalKey();
  static final profileSettingsKey = GlobalKey();
  static final appSettingsKey = GlobalKey();
  static final languageSettingsKey = GlobalKey();
  static final privacyPolicyKey = GlobalKey();
  static final termsConditionsKey = GlobalKey();
  static final profileSectionKey = GlobalKey();
  static final subscriptionSectionKey = GlobalKey();
  static final notificationSettingsKey = GlobalKey();
  static final helpSectionKey = GlobalKey();
  static final logoutButtonKey = GlobalKey();
  static final phoneEditKey = GlobalKey();
  static final salonSwitcherKey = GlobalKey();
  
  /// Get settings tour keys in logical order
  static List<GlobalKey> getSettingsTourKeys() => [
    nameEditKey,
    phoneEditKey,
    themeToggleKey,
    salonManagementKey,
    userSettingsKey,
    profileSettingsKey,
    appSettingsKey,
  ];
}

/// Calendar page tour keys
class CalendarTourKeys {
  static final calendarSettingsKey = GlobalKey();
  static final staffSelectionKey = GlobalKey();
  static final timeSlotKey = GlobalKey();
  static final fabKey = GlobalKey();
  static final appointmentDetailsKey = GlobalKey();
  static final settingsButtonKey = GlobalKey();
  static final calendarViewKey = GlobalKey();
  static final tapToCreateKey = GlobalKey();
  static final addAppointmentKey = GlobalKey();
  static final viewToggleKey = GlobalKey();
  static final todayButtonKey = GlobalKey();
  static final dragDropKey = GlobalKey();
  
  /// Get calendar tour keys in logical order
  static List<GlobalKey> getCalendarTourKeys() => [
    settingsButtonKey,
    calendarViewKey,
    staffSelectionKey,
    timeSlotKey,
    tapToCreateKey,
    addAppointmentKey,
    viewToggleKey,
    todayButtonKey,
    dragDropKey,
    appointmentDetailsKey,
  ];
}

/// Clients page tour keys
class ClientsTourKeys {
  static final clientsListKey = GlobalKey();
  static final addClientKey = GlobalKey();
  static final searchClientKey = GlobalKey();
  static final clientDetailsKey = GlobalKey();
  
  /// Get clients tour keys in logical order
  static List<GlobalKey> getClientsTourKeys() => [
    searchClientKey,
    clientsListKey,
    addClientKey,
    clientDetailsKey,
  ];
}



/// Notifications page tour keys
class NotificationsTourKeys {
  static final notificationsListKey = GlobalKey();
  static final smsKey = GlobalKey();
  static final invitationsKey = GlobalKey();
  static final settingsKey = GlobalKey(); // Add the missing key
  
  /// Get notifications tour keys in logical order
  static List<GlobalKey> getNotificationsTourKeys() => [
    notificationsListKey,
    smsKey,
    invitationsKey,
    settingsKey, // Include in the tour keys list
  ];
}

/// Salon management page tour keys
class SalonTourKeys {
  static final profileTabKey = GlobalKey();
  static final subscriptionKey = GlobalKey();
  static final servicesManagementKey = GlobalKey();
  static final teamManagementKey = GlobalKey();
  static final workingHoursKey = GlobalKey();
  static final smsRemindersKey = GlobalKey();
  // Removed unused keys: reportsKey, notificationsKey, reviewsKey, contactKey

  /// Get salon tour keys in logical order
  static List<GlobalKey> getSalonTourKeys() => [
    subscriptionKey,
    teamManagementKey,
    servicesManagementKey,
    workingHoursKey,
    smsRemindersKey,
  ];
}

