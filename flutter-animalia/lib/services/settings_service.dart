import '../models/api_response.dart';
import '../models/salon_settings.dart';
import 'api_service.dart';

class SettingsService {
  // Get salon settings
  static Future<ApiResponse<SalonSettings>> getSalonSettings() async {
    final response = await ApiService.get<SalonSettings>(
      '/api/settings/salon',
      fromJson: (data) => SalonSettings.fromJson(data),
    );

    return response;
  }

  // Update salon settings
  static Future<ApiResponse<SalonSettings>> updateSalonSettings(SalonSettings settings) async {
    final response = await ApiService.put<SalonSettings>(
      '/api/settings/salon',
      body: settings.toJson(),
      fromJson: (data) => SalonSettings.fromJson(data),
    );

    return response;
  }

  // Update salon name only
  static Future<ApiResponse<SalonSettings>> updateSalonName(String name) async {
    final response = await ApiService.put<SalonSettings>(
      '/api/settings/salon',
      body: {'name': name},
      fromJson: (data) => SalonSettings.fromJson(data),
    );

    return response;
  }

  // Update contact information
  static Future<ApiResponse<SalonSettings>> updateContactInfo({
    String? phone,
    String? email,
    String? address,
    String? website,
  }) async {
    final body = <String, dynamic>{};
    
    if (phone != null) body['phone'] = phone;
    if (email != null) body['email'] = email;
    if (address != null) body['address'] = address;
    if (website != null) body['website'] = website;

    final response = await ApiService.put<SalonSettings>(
      '/api/settings/salon',
      body: body,
      fromJson: (data) => SalonSettings.fromJson(data),
    );

    return response;
  }

  // Get working hours
  static Future<ApiResponse<Map<String, dynamic>>> getWorkingHours() async {
    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/working-hours',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Update working hours
  static Future<ApiResponse<Map<String, dynamic>>> updateWorkingHours(
    Map<String, dynamic> workingHours,
  ) async {
    final response = await ApiService.put<Map<String, dynamic>>(
      '/api/working-hours',
      body: workingHours,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Update business hours for specific day
  static Future<ApiResponse<Map<String, dynamic>>> updateDayHours(
    int dayOfWeek,
    bool isWorkDay,
    int? openTime,
    int? closeTime,
  ) async {
    final body = <String, dynamic>{
      'dayOfWeek': dayOfWeek,
      'isWorkDay': isWorkDay,
      if (openTime != null) 'openTime': openTime,
      if (closeTime != null) 'closeTime': closeTime,
    };

    final response = await ApiService.put<Map<String, dynamic>>(
      '/api/working-hours/day',
      body: body,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Update lunch break
  static Future<ApiResponse<Map<String, dynamic>>> updateLunchBreak(
    int startHour,
    int endHour,
  ) async {
    final body = <String, dynamic>{
      'lunchBreak': {
        'start': startHour,
        'end': endHour,
      },
    };

    final response = await ApiService.put<Map<String, dynamic>>(
      '/api/working-hours',
      body: body,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Add holiday
  static Future<ApiResponse<SalonSettings>> addHoliday(DateTime holiday, {String? description}) async {
    final body = <String, dynamic>{
      'holiday': holiday.toIso8601String().split('T')[0],
      if (description != null) 'description': description,
    };

    final response = await ApiService.post<SalonSettings>(
      '/api/settings/salon/holidays',
      body: body,
      fromJson: (data) => SalonSettings.fromJson(data),
    );

    return response;
  }

  // Remove holiday
  static Future<ApiResponse<SalonSettings>> removeHoliday(DateTime holiday) async {
    final response = await ApiService.delete<SalonSettings>(
      '/api/settings/salon/holidays/${holiday.toIso8601String().split('T')[0]}',
      fromJson: (data) => SalonSettings.fromJson(data),
    );

    return response;
  }

  // Update notification settings
  static Future<ApiResponse<SalonSettings>> updateNotificationSettings(
    Map<String, dynamic> notificationSettings,
  ) async {
    final body = <String, dynamic>{
      'notificationSettings': notificationSettings,
    };

    final response = await ApiService.put<SalonSettings>(
      '/api/settings/salon',
      body: body,
      fromJson: (data) => SalonSettings.fromJson(data),
    );

    return response;
  }

  // Update SMS settings
  static Future<ApiResponse<SalonSettings>> updateSmsSettings(
    Map<String, dynamic> smsSettings,
  ) async {
    final body = <String, dynamic>{
      'smsSettings': smsSettings,
    };

    final response = await ApiService.put<SalonSettings>(
      '/api/settings/salon',
      body: body,
      fromJson: (data) => SalonSettings.fromJson(data),
    );

    return response;
  }

  // Get app configuration
  static Future<ApiResponse<Map<String, dynamic>>> getAppConfig() async {
    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/settings/app-config',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Update app configuration
  static Future<ApiResponse<Map<String, dynamic>>> updateAppConfig(
    Map<String, dynamic> config,
  ) async {
    final response = await ApiService.put<Map<String, dynamic>>(
      '/api/settings/app-config',
      body: config,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Backup settings
  static Future<ApiResponse<String>> backupSettings() async {
    final response = await ApiService.post<String>(
      '/api/settings/backup',
      fromJson: (data) => data.toString(),
    );

    return response;
  }

  // Restore settings from backup
  static Future<ApiResponse<SalonSettings>> restoreSettings(String backupData) async {
    final response = await ApiService.post<SalonSettings>(
      '/api/settings/restore',
      body: {'backupData': backupData},
      fromJson: (data) => SalonSettings.fromJson(data),
    );

    return response;
  }

  // Reset settings to default
  static Future<ApiResponse<SalonSettings>> resetToDefaults() async {
    final response = await ApiService.post<SalonSettings>(
      '/api/settings/reset',
      fromJson: (data) => SalonSettings.fromJson(data),
    );

    return response;
  }

  // Validate salon settings
  static String? validateSalonSettings(SalonSettings settings) {
    if (settings.name.trim().isEmpty) {
      return 'Numele salonului este obligatoriu';
    }

    if (settings.phone.trim().isEmpty) {
      return 'Numărul de telefon este obligatoriu';
    }

    if (settings.email.trim().isEmpty) {
      return 'Adresa de email este obligatorie';
    }

    if (!_isValidEmail(settings.email)) {
      return 'Adresa de email nu este validă';
    }

    if (settings.address.trim().isEmpty) {
      return 'Adresa salonului este obligatorie';
    }

    return null; // No validation errors
  }

  // Validate working hours
  static String? validateWorkingHours(Map<String, dynamic> workingHours) {
    final workDays = workingHours['workDays'] as List<int>?;
    final openTime = workingHours['openTime'] as int?;
    final closeTime = workingHours['closeTime'] as int?;

    if (workDays == null || workDays.isEmpty) {
      return 'Cel puțin o zi de lucru este obligatorie';
    }

    if (openTime == null || closeTime == null) {
      return 'Orele de deschidere și închidere sunt obligatorii';
    }

    if (openTime >= closeTime) {
      return 'Ora de deschidere trebuie să fie înainte de ora de închidere';
    }

    if (openTime < 0 || openTime > 23 || closeTime < 1 || closeTime > 24) {
      return 'Orele trebuie să fie între 0 și 24';
    }

    // Validate lunch break if present
    final lunchBreak = workingHours['lunchBreak'] as Map<String, dynamic>?;
    if (lunchBreak != null) {
      final lunchStart = lunchBreak['start'] as int?;
      final lunchEnd = lunchBreak['end'] as int?;

      if (lunchStart != null && lunchEnd != null) {
        if (lunchStart >= lunchEnd) {
          return 'Ora de început a pauzei trebuie să fie înainte de ora de sfârșit';
        }

        if (lunchStart < openTime || lunchEnd > closeTime) {
          return 'Pauza de masă trebuie să fie în intervalul de lucru';
        }
      }
    }

    return null; // No validation errors
  }

  // Helper method to validate email format
  static bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }

  // Get system status
  static Future<ApiResponse<Map<String, dynamic>>> getSystemStatus() async {
    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/settings/system-status',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Export settings
  static Future<ApiResponse<String>> exportSettings({String format = 'json'}) async {
    final response = await ApiService.post<String>(
      '/api/settings/export',
      body: {'format': format},
      fromJson: (data) => data.toString(),
    );

    return response;
  }
}
