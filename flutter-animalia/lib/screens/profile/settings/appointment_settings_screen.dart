import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../providers/appointment_settings_provider.dart';

class AppointmentSettingsScreen extends StatefulWidget {
  const AppointmentSettingsScreen({super.key});

  @override
  State<AppointmentSettingsScreen> createState() => _AppointmentSettingsScreenState();
}

class _AppointmentSettingsScreenState extends State<AppointmentSettingsScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);
    try {
      await context.read<AppointmentSettingsProvider>().loadSettings();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare la încărcarea setărilor: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _saveSettings() async {
    setState(() => _isLoading = true);
    try {
      await context.read<AppointmentSettingsProvider>().saveSettings();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Setările au fost salvate cu succes'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare la salvarea setărilor: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Setări Programări'),
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveSettings,
            child: const Text('Salvează'),
          ),
        ],
      ),
      body: Stack(
        children: [
          Consumer<AppointmentSettingsProvider>(
            builder: (context, provider, child) {
              return Stack(
                children: [
                  SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionHeader('Finalizare Automată'),
                    const SizedBox(height: 16),
                    _buildAutoFinalizationCard(provider),
                    const SizedBox(height: 24),

                    _buildSectionHeader('Notificări'),
                    const SizedBox(height: 16),
                    _buildNotificationCard(provider),
                    const SizedBox(height: 24),

                    _buildSectionHeader('Mesaj Finalizare'),
                    const SizedBox(height: 16),
                    _buildFinalizationMessageCard(provider),
                    const SizedBox(height: 80), // Space for save button
                  ],
                ),
              ),

              // Loading overlay
              if (provider.isLoading)
                Container(
                  color: Colors.black.withOpacity(0.3),
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
            ],
          );
        },
      ),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.onSurface,
      ),
    );
  }

  Widget _buildAutoFinalizationCard(AppointmentSettingsProvider provider) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Auto-finalizează programări când trece ora',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Programările vor fi marcate automat ca finalizate când trece ora de sfârșit',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: provider.autoFinalizeEnabled,
                  onChanged: provider.isLoading ? null : (value) async {
                    await provider.setAutoFinalize(value);
                  },
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationCard(AppointmentSettingsProvider provider) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.notifications_active,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notificare programare nefinalizată când trece ora',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Primești notificare pentru programările care nu au fost finalizate manual',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: provider.overdueNotificationsEnabled,
                  onChanged: (provider.autoFinalizeEnabled || provider.isLoading)
                    ? null // Disable if auto-finalize is enabled or loading
                    : (value) async => await provider.setOverdueNotifications(value),
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),
            if (provider.autoFinalizeEnabled)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'Dezactivat deoarece auto-finalizarea este activă',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinalizationMessageCard(AppointmentSettingsProvider provider) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.message,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Mesaje finalizare',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // SMS completion message toggle
            Row(
              children: [
                Icon(
                  Icons.sms,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Mesaj SMS la finalizare',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Trimite SMS automat când programarea este finalizată',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: provider.smsCompletionEnabled,
                  onChanged: provider.isLoading ? null : (value) async {
                    await provider.setSmsCompletion(value);
                  },
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),

            if (provider.smsCompletionEnabled) ...[
              const SizedBox(height: 16),
              TextFormField(
                initialValue: provider.finalizationMessage,
                decoration: const InputDecoration(
                  labelText: 'Mesaj SMS finalizare',
                  hintText: 'Animalul dumneavoastră este gata pentru ridicare!',
                  border: OutlineInputBorder(),
                  helperText: 'Acest mesaj va fi trimis automat clientului prin SMS',
                ),
                maxLines: 3,
                onChanged: (value) async => await provider.setFinalizationMessage(value),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
