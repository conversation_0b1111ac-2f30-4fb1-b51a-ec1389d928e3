import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../models/sms_settings.dart';
import '../../../models/sms_reminder_timing.dart';
import '../../../providers/subscription_provider.dart';
import '../../../services/sms_message_preview_service.dart';
import '../../../services/sms_quota_service.dart';
import '../../../services/sms_settings_service.dart';
import '../../../widgets/permission_guard.dart';
import 'sms_template_management_screen.dart';

class SmsRemindersScreen extends StatefulWidget {
  const SmsRemindersScreen({Key? key}) : super(key: key);

  @override
  State<SmsRemindersScreen> createState() => _SmsRemindersScreenState();
}

class _SmsRemindersScreenState extends State<SmsRemindersScreen> with WidgetsBindingObserver {
  bool _smsEnabled = true;
  bool _appointmentConfirmations = true;
  bool _dayBeforeReminders = true;
  bool _followUpMessages = false;

  int _remainingSms = 0;

  SmsSettings? _currentSettings;
  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  // Preview section state
  bool _showPreview = false;

  // Reminder timing state
  List<SmsReminderTiming> _reminderTimings = [];
  bool _isLoadingTimings = false;


  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadSmsSettings();
    _loadReminderTimings();
    _loadRemainingSmsFromBackend(); // Prioritize backend data on init

    // Listen to SMS count changes for real-time updates
    SmsQuotaService.smsCountNotifier.addListener(_onSmsCountChanged);

    // Listen to subscription changes to refresh SMS quota
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final subscriptionProvider = context.read<SubscriptionProvider>();
      subscriptionProvider.addListener(_onSubscriptionChanged);
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    SmsQuotaService.smsCountNotifier.removeListener(_onSmsCountChanged);

    // Remove subscription listener
    try {
      final subscriptionProvider = context.read<SubscriptionProvider>();
      subscriptionProvider.removeListener(_onSubscriptionChanged);
    } catch (e) {
      // Context might be disposed
    }

    super.dispose();
  }

  /// Called when SMS count changes
  void _onSmsCountChanged() {
    if (mounted) {
      setState(() {
        _remainingSms = SmsQuotaService.smsCountNotifier.value;
      });
    }
  }

  /// Called when subscription changes
  void _onSubscriptionChanged() {
    if (mounted) {
      // Refresh SMS count when subscription changes
      _loadRemainingSmsFromBackend();
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // Refresh SMS counter when app resumes from background
    if (state == AppLifecycleState.resumed) {
      _loadRemainingSmsFromBackend();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh SMS counter when screen becomes visible again
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadRemainingSmsFromBackend();
      }
    });
  }

  /// Load reminder timings from backend
  Future<void> _loadReminderTimings() async {
    setState(() {
      _isLoadingTimings = true;
    });

    try {
      // For now, create some default timings
      // TODO: Replace with actual API call when backend is ready
      _reminderTimings = [
        SmsReminderTiming(
          id: '1',
          salonId: 'current-salon',
          reminderType: SmsReminderType.dayBefore,
          hoursBefore: 24,
          isEnabled: true,
          description: 'Cu o zi înainte',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SmsReminderTiming(
          id: '2',
          salonId: 'current-salon',
          reminderType: SmsReminderType.hourBefore,
          hoursBefore: 6,
          isEnabled: false,
          description: 'Cu 6 ore înainte',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      setState(() {
        _isLoadingTimings = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingTimings = false;
      });
      debugPrint('Error loading reminder timings: $e');
    }
  }

  /// Load SMS settings from backend
  Future<void> _loadSmsSettings() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await SmsSettingsService.getSmsSettings();

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
          _smsEnabled = response.data!.enabled;
          _appointmentConfirmations = response.data!.appointmentConfirmations;
          _dayBeforeReminders = response.data!.dayBeforeReminders;
          _followUpMessages = response.data!.followUpMessages;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.error ?? 'Failed to load SMS settings';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading SMS settings: $e';
        _isLoading = false;
      });
    }
  }

  /// Load remaining SMS count from local storage
  Future<void> _loadRemainingSms() async {
    final count = await SmsQuotaService.getRemainingSms();
    if (mounted) {
      setState(() {
        _remainingSms = count;
      });
    }
  }

  /// Load remaining SMS count from backend (preferred)
  Future<void> _loadRemainingSmsFromBackend() async {
    try {
      final success = await SmsQuotaService.refreshFromBackend();
      if (success) {
        final count = await SmsQuotaService.getRemainingSms();
        if (mounted) {
          setState(() {
            _remainingSms = count;
          });
        }
      } else {
        // Fallback to local storage if backend fails
        await _loadRemainingSms();
      }
    } catch (e) {
      debugPrint('Error loading SMS count from backend: $e');
      // Fallback to local storage
      await _loadRemainingSms();
    }
  }

  /// Handle pull-to-refresh
  Future<void> _handleRefresh() async {
    try {
      // Refresh SMS settings, timings, and quota from backend
      await Future.wait([
        _loadSmsSettings(),
        _loadReminderTimings(),
        _loadRemainingSmsFromBackend(),
      ]);
    } catch (e) {
      debugPrint('Error during refresh: $e');
      _showErrorSnackBar('Eroare la actualizarea datelor');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'SMS Reminders',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Configurează SMS-urile automate pentru clienți. Necesită abonament pentru mai multe SMS-uri.')),
              );
            },
            tooltip: 'Ajutor',
          ),
        ],
      ),
      body: PermissionGuard(
        requireManagementAccess: true,
        fallback: _buildNoPermissionView(),
        child: _isLoading
          ? Center(child: CircularProgressIndicator(color: Theme.of(context).colorScheme.onSurface))
          : _error != null
            ? _buildErrorView()
            : Column(
                children: [
                  // Scrollable content with pull-to-refresh
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: _handleRefresh,
                      child: SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(), // Enables pull-to-refresh even when content doesn't scroll
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // SMS Settings Card
                            _buildSmsSettingsCard(),
                            SizedBox(height: 24),

                            // Reminder Timing Configuration
                            _buildReminderTimingCard(),
                            SizedBox(height: 24),

                            // Template Management Card
                            _buildTemplateManagementCard(),
                            SizedBox(height: 16),
                            _buildMessageTemplatesCard(),
                            SizedBox(height: 24),

                            SizedBox(height: 80), // Space for fixed button
                          ],
                        ),
                      ),
                    ),
                  ),
                  // Fixed save button at bottom
                  _buildFixedSaveButton(),
                ],
              ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.onSurface,
      ),
    );
  }

  Widget _buildSmsSettingsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Setări Notificări SMS',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 8),
            Text(
              "SMS rămase: $_remainingSms",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 16),
            _buildSwitchTile(
              title: 'SMS automat',
              subtitle: 'Activează trimiterea automată a SMS-urilor',
              value: _smsEnabled,
              onChanged: _isSaving ? (value) {} : (value) {
                _updateSetting('enabled', value);
                // Dacă SMS automat este dezactivat, dezactivează și celelalte opțiuni
                if (!value) {
                  _updateSetting('appointmentConfirmations', false);
                  _updateSetting('dayBeforeReminders', false);
                  _updateSetting('followUpMessages', false);
                }
              },
            ),
            SizedBox(height: 8),
            _buildSwitchTile(
              title: 'Confirmări programări',
              subtitle: 'Trimite SMS de confirmare la programare',
              value: _smsEnabled ? _appointmentConfirmations : false,
              onChanged: _isSaving ? null : (value) {
                if (!_smsEnabled && value) {
                  // Dacă se activează o opțiune și SMS automat e dezactivat, activează și SMS automat
                  _updateSetting('enabled', true);
                }
                _updateSetting('appointmentConfirmations', value);
              },
            ),
            _buildSwitchTile(
              title: 'Reminder cu o zi înainte',
              subtitle: 'Trimite SMS cu 24h înainte de programare',
              value: _smsEnabled ? _dayBeforeReminders : false,
              onChanged: _isSaving ? null : (value) {
                if (!_smsEnabled && value) {
                  // Dacă se activează o opțiune și SMS automat e dezactivat, activează și SMS automat
                  _updateSetting('enabled', true);
                }
                _updateSetting('dayBeforeReminders', value);
              },
            ),
            _buildSwitchTile(
              title: 'Mesaje de follow-up',
              subtitle: 'Trimite SMS după finalizarea serviciului',
              value: _smsEnabled ? _followUpMessages : false,
              onChanged: _isSaving ? null : (value) {
                if (!_smsEnabled && value) {
                  // Dacă se activează o opțiune și SMS automat e dezactivat, activează și SMS automat
                  _updateSetting('enabled', true);
                }
                _updateSetting('followUpMessages', value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool>? onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged ?? (_) {},
          ),
        ],
      ),
    );
  }


  Widget _buildReminderTimingCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Reminder-uri SMS',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                TextButton.icon(
                  onPressed: _addNewReminder,
                  icon: Icon(Icons.add, size: 20),
                  label: Text('Adaugă'),
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              'Când să fie trimise reminder-urile înainte de programări',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            SizedBox(height: 16),
            if (_isLoadingTimings)
              Center(child: CircularProgressIndicator())
            else
              _buildReminderRows(),
            SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateManagementCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Template-uri SMS',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                TextButton.icon(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => SmsTemplateManagementScreen(),
                      ),
                    );
                  },
                  icon: Icon(Icons.edit, size: 20),
                  label: Text('Editează'),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              'Personalizează mesajele SMS trimise clienților.',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageTemplatesCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Template-uri Mesaje SMS',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            TextButton.icon(
              onPressed: () {
                setState(() {
                  _showPreview = !_showPreview;
                });
              },
              icon: Icon(
                _showPreview ? Icons.visibility_off : Icons.visibility,
                size: 20,
              ),
              label: Text(
                _showPreview ? 'Ascunde' : 'Previzualizare',
                style: TextStyle(fontSize: 14),
              ),
            ),
            Text(
              'Mesajele SMS sunt trimise automat clienților folosind template-urile de mai jos. Toate mesajele sunt personalizate cu informațiile programării.',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            if (_showPreview) ...[
              SizedBox(height: 16),
              _buildEnhancedMessagePreview(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMessagePreviewSection() {
    final sampleData = SmsMessagePreviewService.getSampleData();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.phone_android,
                size: 20,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: 8),
              Text(
                'Previzualizare Mesaje SMS',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            'Exemplu cu datele: ${sampleData['clientName']} și ${sampleData['petName']}',
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontStyle: FontStyle.italic,
            ),
          ),
          SizedBox(height: 16),

          // Appointment Confirmations Preview
          if (_appointmentConfirmations) ...[
            _buildMessagePreviewItem(
              'Confirmare Programare',
              SmsMessagePreviewService.buildAppointmentScheduledMessage(
                salonName: sampleData['salonName'],
                clientName: sampleData['clientName'],
                petName: sampleData['petName'],
                salonAddress: sampleData['salonAddress'],
                salonPhone: sampleData['salonPhone'],
                appointmentDate: sampleData['appointmentDate'],
                startTime: sampleData['startTime'],
              ),
              Icons.event_available,
              enabled: _appointmentConfirmations,
            ),
            SizedBox(height: 12),
          ],

          // Day Before Reminders Preview
          if (_dayBeforeReminders) ...[
            _buildMessagePreviewItem(
              'Reminder cu o zi înainte',
              SmsMessagePreviewService.buildDayBeforeReminderMessage(
                salonName: sampleData['salonName'],
                clientName: sampleData['clientName'],
                petName: sampleData['petName'],
                salonAddress: sampleData['salonAddress'],
                salonPhone: sampleData['salonPhone'],
                appointmentDate: sampleData['appointmentDate'],
                startTime: sampleData['startTime'],
              ),
              Icons.schedule,
              enabled: _dayBeforeReminders,
            ),
            SizedBox(height: 12),
          ],

          // Follow-up Messages Preview
          if (_followUpMessages) ...[
            _buildMessagePreviewItem(
              'Mesaj Follow-up',
              SmsMessagePreviewService.buildFollowUpMessage(
                salonName: sampleData['salonName'],
                clientName: sampleData['clientName'],
                petName: sampleData['petName'],
                salonPhone: sampleData['salonPhone'],
              ),
              Icons.feedback,
              enabled: _followUpMessages,
            ),
            SizedBox(height: 12),
          ],

          // Additional message variants
          _buildMessageVariantsSection(sampleData),
        ],
      ),
    );
  }



  Widget _buildMessagePreviewItem(String title, String message, IconData icon, {required bool enabled}) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: enabled
          ? Theme.of(context).colorScheme.surface
          : Theme.of(context).colorScheme.surface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: enabled
            ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
            : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: enabled
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: enabled
                      ? Theme.of(context).colorScheme.onSurface
                      : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),
              ),
              if (!enabled)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Dezactivat',
                    style: TextStyle(
                      fontSize: 10,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              message,
              style: TextStyle(
                fontSize: 12,
                color: enabled
                  ? Theme.of(context).colorScheme.onSurfaceVariant
                  : Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageVariantsSection(Map<String, dynamic> sampleData) {
    return ExpansionTile(
      title: Text(
        'Variante de mesaje',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
      subtitle: Text(
        'Mesajele se adaptează în funcție de informațiile disponibile',
        style: TextStyle(
          fontSize: 12,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      ),
      children: [
        Padding(
          padding: const EdgeInsets.all(8),
          child: Column(
            children: [
              _buildMessagePreviewItem(
                'Cu toate informațiile',
                SmsMessagePreviewService.buildAppointmentScheduledMessage(
                  salonName: sampleData['salonName'],
                  clientName: sampleData['clientName'],
                  petName: sampleData['petName'],
                  salonAddress: sampleData['salonAddress'],
                  salonPhone: sampleData['salonPhone'],
                  appointmentDate: sampleData['appointmentDate'],
                  startTime: sampleData['startTime'],
                ),
                Icons.check_circle,
                enabled: true,
              ),
              SizedBox(height: 8),
              _buildMessagePreviewItem(
                'Fără numele animalului',
                SmsMessagePreviewService.buildAppointmentScheduledMessage(
                  salonName: sampleData['salonName'],
                  clientName: sampleData['clientName'],
                  petName: null, // No pet name
                  salonAddress: sampleData['salonAddress'],
                  salonPhone: sampleData['salonPhone'],
                  appointmentDate: sampleData['appointmentDate'],
                  startTime: sampleData['startTime'],
                ),
                Icons.pets,
                enabled: true,
              ),
              SizedBox(height: 8),
              _buildMessagePreviewItem(
                'Informații minime',
                SmsMessagePreviewService.buildAppointmentScheduledMessage(
                  salonName: null, // No salon name
                  clientName: null, // No client name
                  petName: null, // No pet name
                  salonAddress: sampleData['salonAddress'],
                  salonPhone: sampleData['salonPhone'],
                  appointmentDate: sampleData['appointmentDate'],
                  startTime: sampleData['startTime'],
                ),
                Icons.info,
                enabled: true,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFixedSaveButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
    );
  }

  Widget _buildReminderRows() {
    return Column(
      children: [
        // Existing reminder rows
        ..._reminderTimings.asMap().entries.map((entry) {
          final index = entry.key;
          final timing = entry.value;
          return _buildReminderRow(timing, index);
        }).toList(),

        // Empty state if no reminders
        if (_reminderTimings.isEmpty)
          _buildEmptyState(),
      ],
    );
  }

  Widget _buildReminderRow(SmsReminderTiming timing, int index) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: timing.isEnabled
            ? Theme.of(context).colorScheme.surface
            : Theme.of(context).colorScheme.surface.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: timing.isEnabled
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.2)
              : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: timing.isEnabled ? 1.5 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: timing.isEnabled ? 0.06 : 0.02),
            blurRadius: timing.isEnabled ? 12 : 4,
            offset: Offset(0, timing.isEnabled ? 3 : 1),
          ),
        ],
      ),
      child: Row(
        children: [
          // Hours dropdown
          Expanded(
            flex: 3,
            child: _buildHoursDropdown(timing, index),
          ),
          SizedBox(width: 16),

          // Toggle switch with animation
          AnimatedScale(
            scale: timing.isEnabled ? 1.0 : 0.95,
            duration: Duration(milliseconds: 200),
            child: Switch.adaptive(
              value: timing.isEnabled,
              onChanged: (value) => _toggleReminder(index, value),
              activeColor: Theme.of(context).colorScheme.primary,
              inactiveThumbColor: Theme.of(context).colorScheme.outline,
            ),
          ),
          SizedBox(width: 8),

          // Delete button with hover effect
          AnimatedOpacity(
            opacity: timing.isEnabled ? 1.0 : 0.5,
            duration: Duration(milliseconds: 200),
            child: IconButton(
              onPressed: () => _deleteReminder(index),
              icon: Icon(
                Icons.delete_outline,
                color: Theme.of(context).colorScheme.error.withValues(alpha: 0.7),
                size: 20,
              ),
              tooltip: 'Șterge reminder',
              constraints: BoxConstraints(minWidth: 40, minHeight: 40),
              style: IconButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Theme.of(context).colorScheme.error,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHoursDropdown(SmsReminderTiming timing, int index) {
    final hourOptions = [
      {'value': 1, 'label': '1 oră înainte'},
      {'value': 2, 'label': '2 ore înainte'},
      {'value': 6, 'label': '6 ore înainte'},
      {'value': 12, 'label': '12 ore înainte'},
      {'value': 24, 'label': '1 zi înainte'},
      {'value': 48, 'label': '2 zile înainte'},
      {'value': 72, 'label': '3 zile înainte'},
    ];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int>(
          value: timing.hoursBefore,
          isExpanded: true,
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            size: 20,
          ),
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          items: hourOptions.map((option) {
            return DropdownMenuItem<int>(
              value: option['value'] as int,
              child: Text(option['label'] as String),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              _updateReminderHours(index, value);
            }
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.schedule_outlined,
            size: 48,
            color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.6),
          ),
          SizedBox(height: 12),
          Text(
            'Niciun reminder configurat',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 4),
          Text(
            'Adaugă primul reminder pentru a notifica clienții',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Add a new reminder with default settings
  void _addNewReminder() {
    // Find the next available hour option that's not already used
    final usedHours = _reminderTimings.map((t) => t.hoursBefore).toSet();
    final availableHours = [1, 2, 6, 12, 24, 48, 72];
    final nextHour = availableHours.firstWhere(
      (hour) => !usedHours.contains(hour),
      orElse: () => 24, // Default to 24 hours if all are used
    );

    final newTiming = SmsReminderTiming(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      salonId: 'current-salon',
      reminderType: SmsReminderType.custom1,
      hoursBefore: nextHour,
      isEnabled: true,
      description: _getDescriptionForHours(nextHour),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    setState(() {
      _reminderTimings.add(newTiming);
    });

    // Auto-save
    _saveReminderTimings();

    // Show feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Reminder adăugat'),
        duration: Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Toggle reminder on/off
  void _toggleReminder(int index, bool isEnabled) {
    setState(() {
      _reminderTimings[index] = _reminderTimings[index].copyWith(
        isEnabled: isEnabled,
        updatedAt: DateTime.now(),
      );
    });

    _saveReminderTimings();
  }

  /// Update reminder hours
  void _updateReminderHours(int index, int hours) {
    setState(() {
      _reminderTimings[index] = _reminderTimings[index].copyWith(
        hoursBefore: hours,
        description: _getDescriptionForHours(hours),
        updatedAt: DateTime.now(),
      );
    });

    _saveReminderTimings();
  }

  /// Delete a reminder
  void _deleteReminder(int index) {
    final timing = _reminderTimings[index];

    setState(() {
      _reminderTimings.removeAt(index);
    });

    _saveReminderTimings();

    // Show undo option
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Reminder șters'),
        duration: Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'Anulează',
          onPressed: () {
            setState(() {
              _reminderTimings.insert(index, timing);
            });
            _saveReminderTimings();
          },
        ),
      ),
    );
  }

  /// Save reminder timings (placeholder for backend integration)
  void _saveReminderTimings() {
    // TODO: Implement backend save
    debugPrint('Saving ${_reminderTimings.length} reminder timings');
  }

  /// Get description for hours
  String _getDescriptionForHours(int hours) {
    switch (hours) {
      case 1: return 'Cu o oră înainte';
      case 2: return 'Cu 2 ore înainte';
      case 6: return 'Cu 6 ore înainte';
      case 12: return 'Cu 12 ore înainte';
      case 24: return 'Cu o zi înainte';
      case 48: return 'Cu 2 zile înainte';
      case 72: return 'Cu 3 zile înainte';
      default: return 'Cu $hours ore înainte';
    }
  }

  Widget _buildEnhancedMessagePreview() {
    // Sample data for realistic preview
    final sampleData = {
      'OWNER_NAME': 'Maria Popescu',
      'PET_NAME': 'Rex',
      'DATE': '15 Martie',
      'TIME': '14:30',
      'SALON_NAME': 'Salon Animalia',
      'PHONE': '0721 123 456',
    };

    final messageTypes = [
      {
        'title': 'Confirmare programare',
        'template': 'Bună ziua {OWNER_NAME}! Programarea pentru {PET_NAME} este confirmată pe {DATE} la ora {TIME}. Vă așteptăm la {SALON_NAME}!',
        'icon': Icons.check_circle_outline,
        'color': Colors.green,
      },
      {
        'title': 'Reminder programare',
        'template': 'Bună ziua {OWNER_NAME}! Vă reamintim că {PET_NAME} are programare mâine la ora {TIME}. Pentru modificări: {PHONE}',
        'icon': Icons.schedule,
        'color': Colors.blue,
      },
      {
        'title': 'Programare finalizată',
        'template': 'Mulțumim {OWNER_NAME}! {PET_NAME} arată minunat! Pentru următoarea programare sunați la {PHONE}.',
        'icon': Icons.pets,
        'color': Colors.purple,
      },
    ];

    return Column(
      children: messageTypes.map((messageType) {
        String populatedMessage = messageType['template'] as String;
        sampleData.forEach((key, value) {
          populatedMessage = populatedMessage.replaceAll('{$key}', value);
        });

        return Container(
          margin: EdgeInsets.only(bottom: 16),
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerLowest,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: (messageType['color'] as Color).withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    messageType['icon'] as IconData,
                    size: 20,
                    color: messageType['color'] as Color,
                  ),
                  SizedBox(width: 8),
                  Text(
                    messageType['title'] as String,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      populatedMessage,
                      style: TextStyle(
                        fontSize: 14,
                        height: 1.4,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${populatedMessage.length} caractere',
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                        Text(
                          '1 SMS',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: populatedMessage.length <= 160
                                ? Colors.green
                                : Colors.orange,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// Update a specific SMS setting
  Future<void> _updateSetting(String settingKey, dynamic value) async {
    setState(() {
      _isSaving = true;
    });

    try {
      // Update local state first for immediate UI feedback
      setState(() {
        switch (settingKey) {
          case 'enabled':
            _smsEnabled = value as bool;
            break;
          case 'appointmentConfirmations':
            _appointmentConfirmations = value as bool;
            break;
          case 'dayBeforeReminders':
            _dayBeforeReminders = value as bool;
            break;
          case 'followUpMessages':
            _followUpMessages = value as bool;
            break;
        }
      });

      // Create update request with current values
      final request = UpdateSmsSettingsRequest(
        enabled: _smsEnabled,
        appointmentConfirmations: _appointmentConfirmations,
        dayBeforeReminders: _dayBeforeReminders,
        followUpMessages: _followUpMessages,
        selectedProvider: 'animalia',
      );

      // Send to backend
      final response = await SmsSettingsService.updateSmsSettings(request);

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
        });
        _showSuccessSnackBar('Setările SMS au fost actualizate cu succes!');
      } else {
        _showErrorSnackBar(response.error ?? 'Eroare la actualizarea setărilor SMS');
        // Revert local state
        await _loadSmsSettings();
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la actualizarea setărilor: $e');
      // Revert local state
      await _loadSmsSettings();
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }



  /// Show success message
  void _showSuccessSnackBar(String message) {
    showTopSnackBar(context, 
      SnackBar(
        content: Text(message),
       
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Show error message
  void _showErrorSnackBar(String message) {
    showTopSnackBar(context, 
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Build error view
  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            SizedBox(height: 16),
            Text(
              'Eroare la încărcarea setărilor SMS',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              _error ?? 'Eroare necunoscută',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadSmsSettings,
              style: ElevatedButton.styleFrom(
               
                
              ),
              child: Text('Încearcă din nou'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build no permission view
  Widget _buildNoPermissionView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_outline,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            SizedBox(height: 16),
            Text(
              'Acces restricționat',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'Doar groomer-ii șefi pot configura setările SMS pentru salon.',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
               
                
              ),
              child: Text('Înapoi'),
            ),
          ],
        ),
      ),
    );
  }
}
