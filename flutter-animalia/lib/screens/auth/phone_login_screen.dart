
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../providers/auth_provider.dart' as app_auth;
import '../../services/ui_notification_service.dart';
import '../../widgets/common/responsive_layout_wrapper.dart';
import 'otp_verification_screen.dart';

class PhoneLoginScreen extends StatefulWidget {
  const PhoneLoginScreen({super.key});

  @override
  State<PhoneLoginScreen> createState() => _PhoneLoginScreenState();
}

class _PhoneLoginScreenState extends State<PhoneLoginScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String _completePhoneNumber = '';

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _verifyPhoneNumber() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      final phoneNumber = _completePhoneNumber;
      final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);

      try {
        final success = await authProvider.verifyPhoneNumber(phoneNumber);

        if (success && mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => OTPVerificationScreen(phoneNumber: phoneNumber),
            ),
          );
        } else if (mounted) {
          UINotificationService.showError(
            context: context,
            title: 'Eroare la trimiterea codului',
            message: authProvider.error ?? 'Nu s-a putut trimite codul de verificare',
          );
        }
      } catch (e) {
        if (mounted) {
          UINotificationService.showError(
            context: context,
            title: 'Eroare neașteptată',
            message: 'A apărut o problemă la verificarea numărului de telefon',
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
      ),
      body: SingleChildScrollView(
        child: ResponsiveFormWrapper(
          child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                const SizedBox(height: 20),
                Center(
                  child: Image.asset(
                    'assets/images/logo-no_bg.png',
                    height: 120,
                    fit: BoxFit.contain,
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'Introduceți numarul de telefon',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: isDark ? AppColors.darkText : AppColors.lightText,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 10),
                Text(
                  'Vă vom trimite un cod de verificare',
                  style: TextStyle(
                    fontSize: 16,
                    color: isDark ? AppColors.darkTextSecondary : AppColors.secondaryText,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                IntlPhoneField(
                  decoration: const InputDecoration(
                    labelText: 'Număr de telefon',
                    hintText: '731 234 567',
                    border: OutlineInputBorder(),
                    helperText: 'Acceptă: 731234567',
                  ),
                  initialCountryCode: 'RO',
                  dropdownIconPosition: IconPosition.trailing,
                  flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                  dropdownTextStyle: TextStyle(
                    color: isDark ? AppColors.darkText : AppColors.lightText,
                  ),
                  showDropdownIcon: true,
                  disableLengthCheck: false,
                  keyboardType: TextInputType.phone,
                  onChanged: (phone) {
                    setState(() {
                      _completePhoneNumber = phone.completeNumber;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.number.isEmpty) {
                      return 'Vă rugăm să introduceți numărul de telefon';
                    }
                    // Accept both 9 digits (731234567) and 10 digits (0731234567)
                    if (value.number.length < 9 || value.number.length > 10) {
                      return 'Numărul trebuie să aibă 9 sau 10 cifre (ex: 731234567 sau 0731234567)';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 30),
                ElevatedButton(
                  onPressed: _isLoading ? null : _verifyPhoneNumber,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading
                      ? CircularProgressIndicator(color: colorScheme.onPrimary)
                      : const Text(
                          'Trimite codul de verificare',
                          style: TextStyle(fontSize: 16),
                        ),
                ),
                const SizedBox(height: 20),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text(
                    'Înapoi la autentificare',
                    style: TextStyle(color: colorScheme.primary),
                  ),
                ),
                ],
              ),
            ),
          ),
      ),
    );
  }
}
