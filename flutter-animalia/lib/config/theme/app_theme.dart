import 'package:flutter/material.dart';

export '../../core/constants/app_strings.dart';
export '../../widgets/common/standard_form_field.dart';
// Export all theme-related classes for easy access
export 'app_dimensions.dart';

/// Centralized color definitions for the Animalia app
///
/// === DUAL-BASE COLOR SYSTEM ===
/// This theme system allows independent customization of light and dark themes.
/// Simply update the two base colors below and all variants are computed automatically.
///
/// LIGHT THEME EXAMPLES:
/// • Medical Blue: Color(0xFF1976D2)
/// • Forest Green: Color(0xFF2E7D32)
/// • Elegant Brown: Color(0xFF8D6E63)
/// • Teal: Color(0xFF00796B)
///
/// DARK THEME EXAMPLES:
/// • Bright Blue: Color(0xFF2196F3)
/// • Bright Green: Color(0xFF4CAF50)
/// • Light Brown: Color(0xFFA1887F)
/// • Bright Teal: Color(0xFF26A69A)
///
/// AUTOMATIC COMPUTATIONS:
/// ✓ Container colors (lighter/darker variants)
/// ✓ Hover states and interactions
/// ✓ Accent variations and harmonies
/// ✓ Professional color relationships
///
/// All colors should be referenced from this class to ensure consistency
class AppColors {
  // === DUAL BASE COLOR SYSTEM ===
  // Change these two colors to customize your entire theme
  static const _lightThemePrimaryColor = Color(0xFFD36135);
  static const _darkThemePrimaryColor = Color(0xFFD36135);

  // === COMPUTED LIGHT THEME COLORS ===
  // Primary color variants for light theme
  static Color get lightPrimary => _lightThemePrimaryColor;
  static Color get lightPrimaryContainer => _lightenColor(_lightThemePrimaryColor, 0.85);
  static Color get lightPrimaryLight => _lightenColor(_lightThemePrimaryColor, 0.15);
  static Color get lightPrimaryDark => _darkenColor(_lightThemePrimaryColor, 0.15);

  // === COMPUTED DARK THEME COLORS ===
  // Primary color variants for dark theme
  static Color get darkPrimary => _darkThemePrimaryColor;
  static Color get darkPrimaryContainer => _darkenColor(_darkThemePrimaryColor, 0.6);
  static Color get darkPrimaryLight => _lightenColor(_darkThemePrimaryColor, 0.15);
  static Color get darkPrimaryDark => _darkenColor(_darkThemePrimaryColor, 0.15);

  // === NEUTRAL COLORS ===
  // Light theme neutral colors
  static const pureWhite = Color(0xFFFFFFFF);       // Primary background and cards
  static const salonWhite = Color(0xFFFFFFFE);      // Subtle white variation for surfaces
  static const lightGray = Color(0xFFF8F8F8);       // Input fields and subtle surfaces
  static const lightGrayVariant = Color(0xFFF5F5F5); // Alternative light surface
  static const lightText = Color(0xFF2C2C2C);       // Primary text - sophisticated dark gray
  static const secondaryText = Color(0xFF6D6D6D);   // Secondary text and labels
  static const bodyText = Color(0xFF424242);        // Body text content

  // Dark theme neutral colors
  static const darkBackground = Color(0xFF000000);     // Pure black background
  static const darkSurface = Color(0xFF1C1C1E);        // Cards and elevated surfaces
  static const darkSurfaceVariant = Color(0xFF2C2C2E); // Secondary surfaces
  static const darkSurfaceAlt = Color(0xFF0A0A0A);     // Subtle black variation
  static const darkBorder = Color(0xFF38383A);         // Borders and dividers
  static const darkText = Color(0xFFFFFFFF);           // Primary text - pure white
  static const darkTextSecondary = Color(0xFF8E8E93);  // Secondary text - iOS gray
  static const darkTextTertiary = Color(0xFF636366);   // Tertiary text - darker gray

  // === STATUS COLORS ===
  // Light theme status colors
  static const lightSuccess = Color(0xFF4CAF50);
  static const lightWarning = Color(0xFFFF9800);
  static const lightError = Color(0xFFDC3545);
  static const lightInfo = Color(0xFF2196F3);

  // Dark theme status colors
  static const darkSuccess = Color(0xFF4CAF50);
  static const darkWarning = Color(0xFFFF9800);
  static const darkError = Color(0xFFFF453A);
  static const darkInfo = Color(0xFF007AFF);
  
  // Appointment status colors
  static const appointmentCancelledGrayLight = Color(0xFFB0BEC5);
  static const appointmentCancelledGrayDark = Color(0xFF455A64);

  /// Generate an extra staff color when predefined colors are exhausted
  static Color generateExtraStaffColor(int index) {
    final hue = (index * 37) % 360;
    return HSLColor.fromAHSL(1.0, hue.toDouble(), 0.65, 0.55).toColor();
  }

  // === FEATURE-SPECIFIC COLORS ===
  // Staff identification colors (for calendar and assignments)
  // Removed green and red colors to avoid confusion with appointment status colors
  static List<Color> get staffColors => [
    Color(0xFFA7727D), // Earthy Rose
    Color(0xFF3E86F5), // Vibrant Blue
    Color(0xFF9B59B6), // Rich Purple
    Color(0xFF20B2AA), // Teal
    Color(0xFFF4B400), // Strong Yellow
    Color(0xFFF57C00), // Orange
    Color(0xFF2196F3), // Bright Blue
    Color(0xFF8E24AA), // Deep Purple
    Color(0xFF00ACC1), // Cyan
    Color(0xFFFF9800), // Amber
  ];

  // Pet size indicator colors (used in pet management)
  static const petSizeSmall = Color(0xFF81C784);   // Light green
  static const petSizeMedium = Color(0xFFFFB74D);  // Light orange
  static const petSizeLarge = Color(0xFFF06292);   // Light pink

  // Grooming service category colors (used in service selection)
  static Color get groomingFullService => lightPrimary;    // Premium full grooming
  static const groomingBathDry = Color(0xFF2196F3);        // Bath & dry services
  static const groomingNailCare = Color(0xFFFF9800);       // Nail trimming
  static const groomingStyling = Color(0xFF9C27B0);        // Creative styling
  static const groomingEarCare = Color(0xFF4CAF50);        // Ear cleaning

  // === BACKWARD COMPATIBILITY ALIASES ===
  // Legacy color names that are still referenced in the codebase as constants
  static const forestGreen = _lightThemePrimaryColor;       // Legacy brand color alias
  static const elegantBrown = _lightThemePrimaryColor;      // Legacy elegant brown alias
  static const elegantBrownContainer = Color(0xFFE3F2FD);   // Legacy container alias (computed from medical blue)
  static const elegantBrownLight = Color(0xFF42A5F5);       // Legacy light variant alias
  static const elegantBrownDark = Color(0xFF1565C0);        // Legacy dark variant alias
  static const darkAccent = _darkThemePrimaryColor;         // Legacy dark accent alias
  static const darkAccentVariant = Color(0xFF64B5F6);       // Legacy dark variant alias
  static const darkAccentContainer = Color(0xFF0D47A1);     // Legacy dark container alias
  static const white = pureWhite;                           // Common white alias
  static const logoBrown = _lightThemePrimaryColor;         // Logo color alias
  static const appBackground = pureWhite;                   // App background alias
  static const taupe = secondaryText;                       // Text color alias
  static const softIvory = lightGray;                       // Surface color alias

  // Social login colors (kept for compatibility)
  static const googleRed = Color(0xFFDB4437);
  static const appleBlack = Color(0xFF000000);


  // === COLOR COMPUTATION METHODS ===
  // Private helper methods to compute color variants programmatically
  
  /// Lightens a color by mixing it with white
  static Color _lightenColor(Color color, double amount) {
    assert(amount >= 0 && amount <= 1, 'Amount must be between 0 and 1');
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// Darkens a color by reducing its lightness
  static Color _darkenColor(Color color, double amount) {
    assert(amount >= 0 && amount <= 1, 'Amount must be between 0 and 1');
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// Adjusts the saturation of a color
  static Color _adjustSaturation(Color color, double amount) {
    assert(amount >= -1 && amount <= 1, 'Amount must be between -1 and 1');
    final hsl = HSLColor.fromColor(color);
    final saturation = (hsl.saturation + amount).clamp(0.0, 1.0);
    return hsl.withSaturation(saturation).toColor();
  }

  /// Creates a harmonious color palette from the primary color
  static List<Color> _generateHarmoniousColors(Color baseColor, int count) {
    final hsl = HSLColor.fromColor(baseColor);
    final colors = <Color>[];
    
    for (int i = 0; i < count; i++) {
      final hueShift = (360.0 / count) * i;
      final newHue = (hsl.hue + hueShift) % 360.0;
      colors.add(hsl.withHue(newHue).toColor());
    }
    
    return colors;
  }
}

/// Centralized theme configuration for the Animalia app
/// Contains both light and dark theme definitions with comprehensive color schemes
class AppTheme {
  /// Internal map for unique staff color assignments
  static Map<String, Color>? _staffColorMap;
  /// Light theme configuration - Clean white with computed primary accents
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: AppColors.lightPrimary,
      scaffoldBackgroundColor: AppColors.pureWhite,
      colorScheme: ColorScheme.light(
        // Primary colors
        primary: AppColors.lightPrimary,
        onPrimary: AppColors.pureWhite,
        primaryContainer: AppColors.lightPrimaryContainer,
        onPrimaryContainer: AppColors.lightPrimary,

        // Secondary colors
        secondary: AppColors.lightPrimaryLight,
        onSecondary: AppColors.pureWhite,
        secondaryContainer: AppColors.lightGrayVariant,
        onSecondaryContainer: AppColors.lightText,

        // Surface colors
        surface: AppColors.pureWhite,
        onSurface: AppColors.lightText,
        surfaceContainerHighest: AppColors.appointmentCancelledGrayLight,
        onSurfaceVariant: AppColors.secondaryText,

        // Error colors
        error: AppColors.lightError,
        onError: AppColors.pureWhite,

        // Outline colors
        outline: AppColors.secondaryText,
        outlineVariant: AppColors.lightPrimary.withValues(alpha: 0.3),
      ),
      // App Bar Theme
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.pureWhite,
        foregroundColor: AppColors.lightText,
        elevation: 0,
        centerTitle: false,
        surfaceTintColor: Colors.transparent,
        titleTextStyle: TextStyle(
          color: AppColors.lightText,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),

      ),

      // Card Theme
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: AppColors.pureWhite,
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.lightGray,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.secondaryText),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.secondaryText),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.lightPrimary, width: 2),
        ),
        labelStyle: const TextStyle(color: AppColors.secondaryText),
        hintStyle: const TextStyle(color: AppColors.secondaryText),
      ),

      // Button Themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.lightPrimary,
          foregroundColor: AppColors.pureWhite,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.lightPrimary,
          side: BorderSide(color: AppColors.lightPrimary),
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.lightPrimary,
        ),
      ),

      // Snackbar Theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColors.lightPrimary,
        contentTextStyle: const TextStyle(color: AppColors.pureWhite),
        actionTextColor: AppColors.pureWhite,
      ),
    );
  }

  /// Dark theme configuration - Pure black with computed primary accents
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: AppColors.darkPrimary,
      scaffoldBackgroundColor: AppColors.darkBackground,
      colorScheme: ColorScheme.dark(
        // Primary colors
        primary: AppColors.darkPrimary,
        onPrimary: AppColors.pureWhite,
        primaryContainer: AppColors.darkPrimaryContainer,
        onPrimaryContainer: AppColors.darkText,

        // Secondary colors
        secondary: AppColors.darkPrimaryLight,
        onSecondary: AppColors.darkBackground,
        secondaryContainer: AppColors.darkSurfaceVariant,
        onSecondaryContainer: AppColors.darkText,

        // Surface colors
        surface: AppColors.darkSurface,
        onSurface: AppColors.darkText,
        surfaceContainerHighest: AppColors.appointmentCancelledGrayDark,
        onSurfaceVariant: AppColors.darkTextSecondary,

        // Error colors
        error: AppColors.darkError,
        onError: AppColors.pureWhite,

        // Outline colors
        outline: AppColors.darkBorder,
        outlineVariant: AppColors.darkBorder.withValues(alpha: 0.3),
      ),

      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.darkSurface,
        foregroundColor: AppColors.darkText,
        elevation: 0,
        centerTitle: false,
        surfaceTintColor: Colors.transparent,
        titleTextStyle: TextStyle(
          color: AppColors.darkText,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: AppColors.darkSurface,
        surfaceTintColor: Colors.transparent,
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.darkSurfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.darkBorder),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.darkBorder),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.darkPrimary, width: 2),
        ),
        labelStyle: const TextStyle(color: AppColors.darkTextSecondary),
        hintStyle: const TextStyle(color: AppColors.darkTextSecondary),
      ),

      // Button Themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.darkPrimary,
          foregroundColor: AppColors.darkBackground,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.darkPrimary,
          side: BorderSide(color: AppColors.darkPrimary),
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.darkPrimary,
        ),
      ),

      // Snackbar Theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColors.darkSurfaceVariant,
        contentTextStyle: const TextStyle(color: AppColors.darkText),
        actionTextColor: AppColors.darkPrimary,
      ),
    );
  }

  /// Helper method to get status color based on theme
  static Color getStatusColor(BuildContext context, String status) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    switch (status.toLowerCase()) {
      case 'success':
      case 'confirmed':
      case 'completed':
        return isDark ? AppColors.darkSuccess : AppColors.lightSuccess;
      case 'warning':
      case 'pending':
        return isDark ? AppColors.darkWarning : AppColors.lightWarning;
      case 'error':
      case 'cancelled':
      case 'canceled':
      case 'anulat':
        return isDark ? AppColors.darkError : AppColors.lightError;
      case 'info':
        return isDark ? AppColors.darkInfo : AppColors.lightInfo;
      default:
        return Theme.of(context).colorScheme.onSurfaceVariant;
    }
  }

  /// Helper method to get staff color for identification
  static Color getStaffColor(String staffId) {
    _staffColorMap ??= <String, Color>{};
    if (_staffColorMap!.containsKey(staffId)) {
      return _staffColorMap![staffId]!;
    }

    final usedColors = _staffColorMap!.values.toSet();
    for (final color in AppColors.staffColors) {
      if (!usedColors.contains(color)) {
        _staffColorMap![staffId] = color;
        return color;
      }
    }

    final newColor = AppColors.generateExtraStaffColor(usedColors.length);
    _staffColorMap![staffId] = newColor;
    return newColor;
  }

  /// === PROGRAMMATIC THEME GENERATION ===
  /// Change the entire app theme by updating the base colors

  /// Generates a complete color scheme from a single primary color
  static Map<String, Color> generateColorScheme(Color primaryColor) {
    return {
      'primary': primaryColor,
      'primaryLight': AppColors._lightenColor(primaryColor, 0.15),
      'primaryDark': AppColors._darkenColor(primaryColor, 0.15),
      'primaryContainer': AppColors._lightenColor(primaryColor, 0.85),
      'primaryAccent': AppColors._adjustSaturation(primaryColor, 0.1),
      'darkContainer': AppColors._darkenColor(primaryColor, 0.6),
    };
  }

  /// Generate light theme with custom primary color
  static ThemeData generateLightTheme(Color primaryColor) {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: primaryColor,
      scaffoldBackgroundColor: AppColors.pureWhite,
      colorScheme: ColorScheme.light(
        // Primary colors
        primary: primaryColor,
        onPrimary: AppColors.pureWhite,
        primaryContainer: AppColors._lightenColor(primaryColor, 0.85),
        onPrimaryContainer: primaryColor,

        // Secondary colors
        secondary: AppColors._lightenColor(primaryColor, 0.15),
        onSecondary: AppColors.pureWhite,
        secondaryContainer: AppColors.lightGrayVariant,
        onSecondaryContainer: AppColors.lightText,

        // Surface colors
        surface: AppColors.pureWhite,
        onSurface: AppColors.lightText,
        surfaceContainerHighest: AppColors.appointmentCancelledGrayLight,
        onSurfaceVariant: AppColors.secondaryText,

        // Error colors
        error: AppColors.lightError,
        onError: AppColors.pureWhite,

        // Outline colors
        outline: AppColors.secondaryText,
        outlineVariant: primaryColor.withValues(alpha: 0.3),
      ),

      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.pureWhite,
        foregroundColor: AppColors.lightText,
        elevation: 0,
        centerTitle: false,
        surfaceTintColor: Colors.transparent,
        titleTextStyle: TextStyle(
          color: AppColors.lightText,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        color: AppColors.pureWhite,
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.lightGray,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.lightError, width: 2),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.lightError, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        hintStyle: TextStyle(color: AppColors.secondaryText),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: AppColors.pureWhite,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        ),
      ),

      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),

      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: BorderSide(color: primaryColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        ),
      ),

      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: AppColors.pureWhite,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),

      // Switch Theme
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.pureWhite;
          }
          return AppColors.secondaryText;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return AppColors.lightGrayVariant;
        }),
      ),

      // Checkbox Theme
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all(AppColors.pureWhite),
        side: BorderSide(color: AppColors.secondaryText, width: 2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),

      // Radio Theme
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return AppColors.secondaryText;
        }),
      ),

      // Progress Indicator Theme
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: primaryColor,
        linearTrackColor: AppColors.lightGrayVariant,
        circularTrackColor: AppColors.lightGrayVariant,
      ),

      // Slider Theme
      sliderTheme: SliderThemeData(
        activeTrackColor: primaryColor,
        inactiveTrackColor: AppColors.lightGrayVariant,
        thumbColor: primaryColor,
        overlayColor: primaryColor.withValues(alpha: 0.2),
        valueIndicatorColor: primaryColor,
        valueIndicatorTextStyle: TextStyle(color: AppColors.pureWhite),
      ),

      // Tab Bar Theme
      tabBarTheme: TabBarThemeData(
        labelColor: primaryColor,
        unselectedLabelColor: AppColors.secondaryText,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: primaryColor, width: 2),
        ),
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: AppColors.pureWhite,
        selectedItemColor: primaryColor,
        unselectedItemColor: AppColors.secondaryText,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // Navigation Rail Theme
      navigationRailTheme: NavigationRailThemeData(
        backgroundColor: AppColors.pureWhite,
        selectedIconTheme: IconThemeData(color: primaryColor),
        unselectedIconTheme: IconThemeData(color: AppColors.secondaryText),
        selectedLabelTextStyle: TextStyle(color: primaryColor),
        unselectedLabelTextStyle: TextStyle(color: AppColors.secondaryText),
      ),

      // Drawer Theme
      drawerTheme: DrawerThemeData(
        backgroundColor: AppColors.pureWhite,
        surfaceTintColor: Colors.transparent,
      ),

      // List Tile Theme
      listTileTheme: ListTileThemeData(
        iconColor: AppColors.secondaryText,
        textColor: AppColors.lightText,
        selectedColor: primaryColor,
        selectedTileColor: primaryColor.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),

      // Chip Theme
      chipTheme: ChipThemeData(
        backgroundColor: AppColors.lightGrayVariant,
        selectedColor: primaryColor.withValues(alpha: 0.2),
        labelStyle: TextStyle(color: AppColors.lightText),
        secondaryLabelStyle: TextStyle(color: AppColors.pureWhite),
        brightness: Brightness.light,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),

      // Dialog Theme
      dialogTheme: DialogThemeData(
        backgroundColor: AppColors.pureWhite,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        titleTextStyle: TextStyle(
          color: AppColors.lightText,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        contentTextStyle: TextStyle(
          color: AppColors.lightText,
          fontSize: 16,
        ),
      ),

      // Snack Bar Theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColors.lightText,
        contentTextStyle: TextStyle(color: AppColors.pureWhite),
        actionTextColor: primaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        behavior: SnackBarBehavior.floating,
      ),

      // Tooltip Theme
      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: AppColors.lightText,
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: TextStyle(color: AppColors.pureWhite),
      ),

      // Divider Theme
      dividerTheme: DividerThemeData(
        color: AppColors.lightGrayVariant,
        thickness: 1,
        space: 1,
      ),

      // Icon Theme
      iconTheme: IconThemeData(
        color: AppColors.secondaryText,
        size: 24,
      ),

      // Primary Icon Theme
      primaryIconTheme: IconThemeData(
        color: primaryColor,
        size: 24,
      ),

      // Text Theme
      textTheme: TextTheme(
        displayLarge: TextStyle(color: AppColors.lightText, fontWeight: FontWeight.w300),
        displayMedium: TextStyle(color: AppColors.lightText, fontWeight: FontWeight.w400),
        displaySmall: TextStyle(color: AppColors.lightText, fontWeight: FontWeight.w400),
        headlineLarge: TextStyle(color: AppColors.lightText, fontWeight: FontWeight.w400),
        headlineMedium: TextStyle(color: AppColors.lightText, fontWeight: FontWeight.w400),
        headlineSmall: TextStyle(color: AppColors.lightText, fontWeight: FontWeight.w400),
        titleLarge: TextStyle(color: AppColors.lightText, fontWeight: FontWeight.w500),
        titleMedium: TextStyle(color: AppColors.lightText, fontWeight: FontWeight.w500),
        titleSmall: TextStyle(color: AppColors.lightText, fontWeight: FontWeight.w500),
        bodyLarge: TextStyle(color: AppColors.lightText, fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(color: AppColors.lightText, fontWeight: FontWeight.w400),
        bodySmall: TextStyle(color: AppColors.secondaryText, fontWeight: FontWeight.w400),
        labelLarge: TextStyle(color: AppColors.lightText, fontWeight: FontWeight.w500),
        labelMedium: TextStyle(color: AppColors.lightText, fontWeight: FontWeight.w500),
        labelSmall: TextStyle(color: AppColors.secondaryText, fontWeight: FontWeight.w500),
      ),
    );
  }

  /// Generate dark theme with custom primary color
  static ThemeData generateDarkTheme(Color primaryColor) {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: primaryColor,
      scaffoldBackgroundColor: AppColors.darkBackground,
      colorScheme: ColorScheme.dark(
        // Primary colors
        primary: primaryColor,
        onPrimary: AppColors.pureWhite,
        primaryContainer: AppColors._darkenColor(primaryColor, 0.6),
        onPrimaryContainer: AppColors.darkText,

        // Secondary colors
        secondary: AppColors._lightenColor(primaryColor, 0.15),
        onSecondary: AppColors.darkBackground,
        secondaryContainer: AppColors.darkSurfaceVariant,
        onSecondaryContainer: AppColors.darkText,

        // Surface colors
        surface: AppColors.darkSurface,
        onSurface: AppColors.darkText,
        surfaceContainerHighest: AppColors.appointmentCancelledGrayDark,
        onSurfaceVariant: AppColors.darkTextSecondary,

        // Error colors
        error: AppColors.darkError,
        onError: AppColors.pureWhite,

        // Outline colors
        outline: AppColors.darkBorder,
        outlineVariant: AppColors.darkBorder.withValues(alpha: 0.3),
      ),

      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.darkSurface,
        foregroundColor: AppColors.darkText,
        elevation: 0,
        centerTitle: false,
        surfaceTintColor: Colors.transparent,
        titleTextStyle: TextStyle(
          color: AppColors.darkText,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        color: AppColors.darkSurface,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.darkSurfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.darkError, width: 2),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.darkError, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        hintStyle: TextStyle(color: AppColors.darkTextSecondary),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: AppColors.pureWhite,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        ),
      ),

      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),

      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: BorderSide(color: primaryColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        ),
      ),

      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: AppColors.pureWhite,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),

      // Switch Theme
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.pureWhite;
          }
          return AppColors.darkTextSecondary;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return AppColors.darkSurfaceVariant;
        }),
      ),

      // Checkbox Theme
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all(AppColors.pureWhite),
        side: BorderSide(color: AppColors.darkBorder, width: 2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),

      // Radio Theme
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return AppColors.darkTextSecondary;
        }),
      ),

      // Progress Indicator Theme
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: primaryColor,
        linearTrackColor: AppColors.darkSurfaceVariant,
        circularTrackColor: AppColors.darkSurfaceVariant,
      ),

      // Slider Theme
      sliderTheme: SliderThemeData(
        activeTrackColor: primaryColor,
        inactiveTrackColor: AppColors.darkSurfaceVariant,
        thumbColor: primaryColor,
        overlayColor: primaryColor.withValues(alpha: 0.2),
        valueIndicatorColor: primaryColor,
        valueIndicatorTextStyle: TextStyle(color: AppColors.pureWhite),
      ),

      // Tab Bar Theme
      tabBarTheme: TabBarThemeData(
        labelColor: primaryColor,
        unselectedLabelColor: AppColors.darkTextSecondary,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: primaryColor, width: 2),
        ),
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: AppColors.darkSurface,
        selectedItemColor: primaryColor,
        unselectedItemColor: AppColors.darkTextSecondary,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // Navigation Rail Theme
      navigationRailTheme: NavigationRailThemeData(
        backgroundColor: AppColors.darkSurface,
        selectedIconTheme: IconThemeData(color: primaryColor),
        unselectedIconTheme: IconThemeData(color: AppColors.darkTextSecondary),
        selectedLabelTextStyle: TextStyle(color: primaryColor),
        unselectedLabelTextStyle: TextStyle(color: AppColors.darkTextSecondary),
      ),

      // Drawer Theme
      drawerTheme: DrawerThemeData(
        backgroundColor: AppColors.darkSurface,
        surfaceTintColor: Colors.transparent,
      ),

      // List Tile Theme
      listTileTheme: ListTileThemeData(
        iconColor: AppColors.darkTextSecondary,
        textColor: AppColors.darkText,
        selectedColor: primaryColor,
        selectedTileColor: primaryColor.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),

      // Chip Theme
      chipTheme: ChipThemeData(
        backgroundColor: AppColors.darkSurfaceVariant,
        selectedColor: primaryColor.withValues(alpha: 0.2),
        labelStyle: TextStyle(color: AppColors.darkText),
        secondaryLabelStyle: TextStyle(color: AppColors.pureWhite),
        brightness: Brightness.dark,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),

      // Dialog Theme
      dialogTheme: DialogThemeData(
        backgroundColor: AppColors.darkSurface,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        titleTextStyle: TextStyle(
          color: AppColors.darkText,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        contentTextStyle: TextStyle(
          color: AppColors.darkText,
          fontSize: 16,
        ),
      ),

      // Snack Bar Theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColors.darkText,
        contentTextStyle: TextStyle(color: AppColors.darkBackground),
        actionTextColor: primaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        behavior: SnackBarBehavior.floating,
      ),

      // Tooltip Theme
      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: AppColors.darkText,
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: TextStyle(color: AppColors.darkBackground),
      ),

      // Divider Theme
      dividerTheme: DividerThemeData(
        color: AppColors.darkBorder,
        thickness: 1,
        space: 1,
      ),

      // Icon Theme
      iconTheme: IconThemeData(
        color: AppColors.darkTextSecondary,
        size: 24,
      ),

      // Primary Icon Theme
      primaryIconTheme: IconThemeData(
        color: primaryColor,
        size: 24,
      ),

      // Text Theme
      textTheme: TextTheme(
        displayLarge: TextStyle(color: AppColors.darkText, fontWeight: FontWeight.w300),
        displayMedium: TextStyle(color: AppColors.darkText, fontWeight: FontWeight.w400),
        displaySmall: TextStyle(color: AppColors.darkText, fontWeight: FontWeight.w400),
        headlineLarge: TextStyle(color: AppColors.darkText, fontWeight: FontWeight.w400),
        headlineMedium: TextStyle(color: AppColors.darkText, fontWeight: FontWeight.w400),
        headlineSmall: TextStyle(color: AppColors.darkText, fontWeight: FontWeight.w400),
        titleLarge: TextStyle(color: AppColors.darkText, fontWeight: FontWeight.w500),
        titleMedium: TextStyle(color: AppColors.darkText, fontWeight: FontWeight.w500),
        titleSmall: TextStyle(color: AppColors.darkText, fontWeight: FontWeight.w500),
        bodyLarge: TextStyle(color: AppColors.darkText, fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(color: AppColors.darkText, fontWeight: FontWeight.w400),
        bodySmall: TextStyle(color: AppColors.darkTextSecondary, fontWeight: FontWeight.w400),
        labelLarge: TextStyle(color: AppColors.darkText, fontWeight: FontWeight.w500),
        labelMedium: TextStyle(color: AppColors.darkText, fontWeight: FontWeight.w500),
        labelSmall: TextStyle(color: AppColors.darkTextSecondary, fontWeight: FontWeight.w500),
      ),
    );
  }

  /// Preview how the app would look with different primary colors
  static ThemeData previewTheme({
    required Color lightPrimary,
    required Color darkPrimary,
    required bool isDark,
  }) {
    if (isDark) {
      return generateDarkTheme(darkPrimary);
    } else {
      return generateLightTheme(lightPrimary);
    }
  }
}
