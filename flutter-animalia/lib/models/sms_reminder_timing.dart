/// SMS reminder timing model for customizable reminder intervals
class SmsReminderTiming {
  final String id;
  final String salonId;
  final SmsReminderType reminderType;
  final int hoursBefore;
  final bool isEnabled;
  final String description;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SmsReminderTiming({
    required this.id,
    required this.salonId,
    required this.reminderType,
    required this.hoursBefore,
    required this.isEnabled,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create SmsReminderTiming from JSON
  factory SmsReminderTiming.fromJson(Map<String, dynamic> json) {
    return SmsReminderTiming(
      id: json['id'] as String,
      salonId: json['salonId'] as String,
      reminderType: SmsReminderType.fromString(json['reminderType'] as String),
      hoursBefore: json['hoursBefore'] as int,
      isEnabled: json['isEnabled'] as bool? ?? true,
      description: json['description'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert SmsReminderTiming to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'salonId': salonId,
      'reminderType': reminderType.name,
      'hoursBefore': hoursBefore,
      'isEnabled': isEnabled,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated values
  SmsReminderTiming copyWith({
    String? id,
    String? salonId,
    SmsReminderType? reminderType,
    int? hoursBefore,
    bool? isEnabled,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SmsReminderTiming(
      id: id ?? this.id,
      salonId: salonId ?? this.salonId,
      reminderType: reminderType ?? this.reminderType,
      hoursBefore: hoursBefore ?? this.hoursBefore,
      isEnabled: isEnabled ?? this.isEnabled,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get human-readable description based on hours
  static String getDescriptionForHours(int hours) {
    if (hours == 1) {
      return 'Cu o oră înainte';
    } else if (hours < 24) {
      return 'Cu $hours ore înainte';
    } else if (hours == 24) {
      return 'Cu o zi înainte';
    } else if (hours == 48) {
      return 'Cu 2 zile înainte';
    } else if (hours % 24 == 0) {
      return 'Cu ${hours ~/ 24} zile înainte';
    } else {
      return 'Cu $hours ore înainte';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmsReminderTiming &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SmsReminderTiming(id: $id, reminderType: $reminderType, hoursBefore: $hoursBefore, isEnabled: $isEnabled)';
  }
}

/// Enum for SMS reminder types
enum SmsReminderType {
  dayBefore('DAY_BEFORE', 'Cu o zi înainte', 24),
  hourBefore('HOUR_BEFORE', 'Cu câteva ore înainte', 6),
  custom1('CUSTOM_1', 'Personalizat 1', 12),
  custom2('CUSTOM_2', 'Personalizat 2', 2),
  custom3('CUSTOM_3', 'Personalizat 3', 1),
  custom4('CUSTOM_4', 'Personalizat 4', 48),
  custom5('CUSTOM_5', 'Personalizat 5', 72);

  const SmsReminderType(this.name, this.displayName, this.defaultHours);

  final String name;
  final String displayName;
  final int defaultHours;

  static SmsReminderType fromString(String value) {
    return SmsReminderType.values.firstWhere(
      (type) => type.name == value,
      orElse: () => throw ArgumentError('Unknown SMS reminder type: $value'),
    );
  }
}

/// Request model for creating SMS reminder timing
class CreateSmsReminderTimingRequest {
  final SmsReminderType reminderType;
  final int hoursBefore;
  final bool isEnabled;

  const CreateSmsReminderTimingRequest({
    required this.reminderType,
    required this.hoursBefore,
    this.isEnabled = true,
  });

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'reminderType': reminderType.name,
      'hoursBefore': hoursBefore,
      'isEnabled': isEnabled,
    };
  }
}

/// Request model for updating SMS reminder timing
class UpdateSmsReminderTimingRequest {
  final int hoursBefore;
  final bool isEnabled;

  const UpdateSmsReminderTimingRequest({
    required this.hoursBefore,
    required this.isEnabled,
  });

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'hoursBefore': hoursBefore,
      'isEnabled': isEnabled,
    };
  }
}

/// Common timing presets
class TimingPreset {
  final String label;
  final int hours;

  const TimingPreset({
    required this.label,
    required this.hours,
  });

  static List<TimingPreset> getCommonPresets() {
    return const [
      TimingPreset(label: '30 minute înainte', hours: 1),
      TimingPreset(label: '2 ore înainte', hours: 2),
      TimingPreset(label: '6 ore înainte', hours: 6),
      TimingPreset(label: '12 ore înainte', hours: 12),
      TimingPreset(label: 'O zi înainte', hours: 24),
      TimingPreset(label: '2 zile înainte', hours: 48),
      TimingPreset(label: '3 zile înainte', hours: 72),
    ];
  }
}
