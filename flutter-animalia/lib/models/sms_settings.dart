/// SMS reminder settings for a salon
class SmsSettings {
  final String salonId;
  final bool enabled;
  final bool appointmentConfirmations;
  final bool dayBeforeReminders;
  final bool followUpMessages;
  final String? selectedProvider;
  final DateTime updatedAt;

  const SmsSettings({
    required this.salonId,
    required this.enabled,
    required this.appointmentConfirmations,
    required this.dayBeforeReminders,
    required this.followUpMessages,
    this.selectedProvider,
    required this.updatedAt,
  });

  /// Create SmsSettings from JSON
  factory SmsSettings.fromJson(Map<String, dynamic> json) {
    return SmsSettings(
      salonId: json['salonId'] as String,
      enabled: json['enabled'] as bool? ?? true,
      appointmentConfirmations: json['appointmentConfirmations'] as bool? ?? false,
      dayBeforeReminders: json['dayBeforeReminders'] as bool? ?? false,
      followUpMessages: json['followUpMessages'] as bool? ?? false,
      selectedProvider: json['selectedProvider'] as String?,
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert SmsSettings to JSON
  Map<String, dynamic> toJson() {
    return {
      'salonId': salonId,
      'enabled': enabled,
      'appointmentConfirmations': appointmentConfirmations,
      'dayBeforeReminders': dayBeforeReminders,
      'followUpMessages': followUpMessages,
      'selectedProvider': selectedProvider,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated values
  SmsSettings copyWith({
    String? salonId,
    bool? enabled,
    bool? appointmentConfirmations,
    bool? dayBeforeReminders,
    bool? followUpMessages,
    String? selectedProvider,
    DateTime? updatedAt,
  }) {
    return SmsSettings(
      salonId: salonId ?? this.salonId,
      enabled: enabled ?? this.enabled,
      appointmentConfirmations: appointmentConfirmations ?? this.appointmentConfirmations,
      dayBeforeReminders: dayBeforeReminders ?? this.dayBeforeReminders,
      followUpMessages: followUpMessages ?? this.followUpMessages,
      selectedProvider: selectedProvider ?? this.selectedProvider,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SmsSettings &&
        other.salonId == salonId &&
        other.enabled == enabled &&
        other.appointmentConfirmations == appointmentConfirmations &&
        other.dayBeforeReminders == dayBeforeReminders &&
        other.followUpMessages == followUpMessages &&
        other.selectedProvider == selectedProvider &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      salonId,
      enabled,
      appointmentConfirmations,
      dayBeforeReminders,
      followUpMessages,
      selectedProvider,
      updatedAt,
    );
  }

  @override
  String toString() {
    return 'SmsSettings(salonId: $salonId, appointmentConfirmations: $appointmentConfirmations, '
        'enabled: $enabled, dayBeforeReminders: $dayBeforeReminders, followUpMessages: $followUpMessages, '
        'selectedProvider: $selectedProvider, updatedAt: $updatedAt)';
  }
}

/// Request model for updating SMS settings
class UpdateSmsSettingsRequest {
  final bool enabled;
  final bool appointmentConfirmations;
  final bool dayBeforeReminders;
  final bool followUpMessages;
  final String? selectedProvider;

  const UpdateSmsSettingsRequest({
    required this.enabled,
    required this.appointmentConfirmations,
    required this.dayBeforeReminders,
    required this.followUpMessages,
    this.selectedProvider,
  });

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'appointmentConfirmations': appointmentConfirmations,
      'dayBeforeReminders': dayBeforeReminders,
      'followUpMessages': followUpMessages,
      'selectedProvider': selectedProvider,
      'enabled': enabled,
    };
  }

  /// Create from SmsSettings
  factory UpdateSmsSettingsRequest.fromSmsSettings(SmsSettings settings) {
    return UpdateSmsSettingsRequest(
      appointmentConfirmations: settings.appointmentConfirmations,
      dayBeforeReminders: settings.dayBeforeReminders,
      followUpMessages: settings.followUpMessages,
      selectedProvider: settings.selectedProvider,
      enabled: settings.enabled,
    );
  }
}
