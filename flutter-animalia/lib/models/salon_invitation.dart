import '../utils/formatters/phone_number_utils.dart';
import 'user_role.dart';

/// Status of a salon invitation
enum InvitationStatus {
  pending('PENDING'),
  accepted('ACCEPTED'),
  declined('DECLINED'),
  expired('EXPIRED');

  const InvitationStatus(this.value);
  final String value;

  static InvitationStatus fromString(String value) {
    switch (value.toUpperCase()) {
      case 'PENDING':
        return InvitationStatus.pending;
      case 'ACCEPTED':
        return InvitationStatus.accepted;
      case 'DECLINED':
        return InvitationStatus.declined;
      case 'EXPIRED':
        return InvitationStatus.expired;
      default:
        throw ArgumentError('Unknown invitation status: $value');
    }
  }

  String get displayName {
    switch (this) {
      case InvitationStatus.pending:
        return 'În așteptare';
      case InvitationStatus.accepted:
        return 'Acceptată';
      case InvitationStatus.declined:
        return 'Refuzată';
      case InvitationStatus.expired:
        return 'Expirată';
    }
  }
}

/// Model for salon invitations
class SalonInvitation {
  final String id;
  final String salonId;
  final String salonName;
  final String invitedByUserId;
  final String invitedByName;
  final String invitedUserPhone;
  final String? invitedUserId; // null if user hasn't registered yet
  final GroomerRole proposedRole;
  final ClientDataPermission proposedClientDataPermission;
  final String? proposedNickname; // Name provided by inviter for the invited person
  final InvitationStatus status;
  final DateTime createdAt;
  final DateTime expiresAt;
  final DateTime? respondedAt;
  final String? message;

  SalonInvitation({
    required this.id,
    required this.salonId,
    required this.salonName,
    required this.invitedByUserId,
    required this.invitedByName,
    required this.invitedUserPhone,
    this.invitedUserId,
    required this.proposedRole,
    required this.proposedClientDataPermission,
    this.proposedNickname,
    required this.status,
    required this.createdAt,
    required this.expiresAt,
    this.respondedAt,
    this.message,
  });

  /// Create from JSON
  factory SalonInvitation.fromJson(Map<String, dynamic> json) {
    return SalonInvitation(
      id: json['id'],
      salonId: json['salonId'],
      salonName: json['salonName'],
      // Backend sends 'inviterUserId' and 'inviterName', map to our field names
      invitedByUserId: json['inviterUserId'] ?? json['invitedByUserId'],
      invitedByName: json['inviterName'] ?? json['invitedByName'],
      invitedUserPhone: json['invitedUserPhone'],
      invitedUserId: json['invitedUserId'],
      proposedRole: GroomerRole.fromString(json['proposedRole']),
      proposedClientDataPermission: ClientDataPermission.fromString(json['proposedClientDataPermission']),
      proposedNickname: json['proposedNickname'],
      status: InvitationStatus.fromString(json['status']),
      // Backend sends both 'invitedAt' and 'createdAt', prefer 'invitedAt' if available
      createdAt: DateTime.parse(json['invitedAt'] ?? json['createdAt']),
      expiresAt: DateTime.parse(json['expiresAt']),
      respondedAt: json['respondedAt'] != null ? DateTime.parse(json['respondedAt']) : null,
      message: json['message'],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'salonId': salonId,
      'salonName': salonName,
      'invitedByUserId': invitedByUserId,
      'invitedByName': invitedByName,
      'invitedUserPhone': invitedUserPhone,
      'invitedUserId': invitedUserId,
      'proposedRole': proposedRole.value,
      'proposedClientDataPermission': proposedClientDataPermission.value,
      'proposedNickname': proposedNickname,
      'status': status.value,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
      'respondedAt': respondedAt?.toIso8601String(),
      'message': message,
    };
  }

  /// Copy with method for immutable updates
  SalonInvitation copyWith({
    String? id,
    String? salonId,
    String? salonName,
    String? invitedByUserId,
    String? invitedByName,
    String? invitedUserPhone,
    String? invitedUserId,
    GroomerRole? proposedRole,
    ClientDataPermission? proposedClientDataPermission,
    String? proposedNickname,
    InvitationStatus? status,
    DateTime? createdAt,
    DateTime? expiresAt,
    DateTime? respondedAt,
    String? message,
  }) {
    return SalonInvitation(
      id: id ?? this.id,
      salonId: salonId ?? this.salonId,
      salonName: salonName ?? this.salonName,
      invitedByUserId: invitedByUserId ?? this.invitedByUserId,
      invitedByName: invitedByName ?? this.invitedByName,
      invitedUserPhone: invitedUserPhone ?? this.invitedUserPhone,
      invitedUserId: invitedUserId ?? this.invitedUserId,
      proposedRole: proposedRole ?? this.proposedRole,
      proposedClientDataPermission: proposedClientDataPermission ?? this.proposedClientDataPermission,
      proposedNickname: proposedNickname ?? this.proposedNickname,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      respondedAt: respondedAt ?? this.respondedAt,
      message: message ?? this.message,
    );
  }

  /// Get formatted phone number for display
  String get formattedInvitedUserPhone => PhoneNumberUtils.formatForDisplay(invitedUserPhone);

  /// Get normalized phone number for API calls
  String get normalizedInvitedUserPhone => PhoneNumberUtils.normalizeForApi(invitedUserPhone);

  /// Check if invitation is still valid
  bool get isValid => status == InvitationStatus.pending && DateTime.now().isBefore(expiresAt);

  /// Check if invitation has expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Get time remaining until expiration
  Duration get timeUntilExpiration => expiresAt.difference(DateTime.now());

  /// Get formatted expiration time
  String get formattedExpirationTime {
    final now = DateTime.now();
    final difference = expiresAt.difference(now);

    if (difference.isNegative) {
      return 'Expirată';
    }

    if (difference.inDays > 0) {
      return '${difference.inDays} zile';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ore';
    } else {
      return '${difference.inMinutes} minute';
    }
  }

  /// Get invitation description for display
  String get description {
    return 'Invitație să te alături echipei ${salonName} ca ${proposedRole.displayName}';
  }
}
