class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final String? error;
  final String? errorCode;
  final Map<String, dynamic>? errorDetails;
  final int? statusCode;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.error,
    this.errorCode,
    this.errorDetails,
    this.statusCode,
  });

  factory ApiResponse.success(T data, {String? message}) {
    return ApiResponse(
      success: true,
      data: data,
      message: message,
      statusCode: 200,
    );
  }

  factory ApiResponse.error(
    String error, {
    int? statusCode,
    String? errorCode,
    Map<String, dynamic>? errorDetails,
  }) {
    return ApiResponse(
      success: false,
      error: error,
      errorCode: errorCode,
      errorDetails: errorDetails,
      statusCode: statusCode ?? 500,
    );
  }

  factory ApiResponse.fromJson(Map<String, dynamic> json, T Function(dynamic) fromJsonT) {
    // Handle structured error format: {success: false, error: {message, code}}
    String? errorMessage;
    String? errorCode;
    Map<String, dynamic>? errorDetails;

    final errorData = json['error'];
    if (errorData is Map<String, dynamic>) {
      errorMessage = errorData['message'];
      errorCode = errorData['code'];
      errorDetails = errorData;
    } else if (errorData is String) {
      errorMessage = errorData;
    }

    return ApiResponse(
      success: json['success'] ?? false,
      data: json['data'] != null ? fromJsonT(json['data']) : null,
      message: json['message'],
      error: errorMessage,
      errorCode: errorCode,
      errorDetails: errorDetails,
      statusCode: json['statusCode'],
    );
  }

  Map<String, dynamic> toJson(dynamic Function(T) toJsonT) {
    return {
      'success': success,
      'data': data != null ? toJsonT(data as T) : null,
      'message': message,
      'error': error,
      'errorCode': errorCode,
      'errorDetails': errorDetails,
      'statusCode': statusCode,
    };
  }

  /// Get user-friendly error message, preferring the detailed message over generic error
  String get userFriendlyError {
    if (error != null && error!.isNotEmpty) {
      return error!;
    }
    return 'A apărut o eroare neașteptată';
  }

  /// Check if this is a specific business logic error
  bool hasErrorCode(String code) {
    return errorCode == code;
  }

  /// Check if this is a scheduling conflict error
  bool get isSchedulingConflict => hasErrorCode('SCHEDULING_CONFLICT');

  /// Check if this is an invalid time slot error
  bool get isInvalidTimeSlot => hasErrorCode('INVALID_TIME_SLOT');

  /// Check if this is a staff unavailable error
  bool get isStaffUnavailable => hasErrorCode('STAFF_UNAVAILABLE');
}

class PaginatedResponse<T> {
  final List<T> items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  PaginatedResponse({
    required this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return PaginatedResponse(
      items: (json['items'] as List).map((item) => fromJsonT(item)).toList(),
      total: json['total'],
      page: json['page'],
      pageSize: json['pageSize'],
      totalPages: json['totalPages'],
    );
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'items': items.map((item) => toJsonT(item)).toList(),
      'total': total,
      'page': page,
      'pageSize': pageSize,
      'totalPages': totalPages,
    };
  }
}
