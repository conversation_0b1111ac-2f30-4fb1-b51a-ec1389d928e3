class Review {
  final String id;
  final String clientId;
  final String appointmentId;
  final String petId;
  final String petName;
  final String service;
  final int rating; // 1-5 stars
  final String comment;
  final DateTime reviewDate;
  final List<String> photoUrls;
  final bool isVerified;
  final String groomerId; // Who performed the service
  final String groomerName;

  Review({
    required this.id,
    required this.clientId,
    required this.appointmentId,
    required this.petId,
    required this.petName,
    required this.service,
    required this.rating,
    required this.comment,
    required this.reviewDate,
    this.photoUrls = const [],
    this.isVerified = false,
    required this.groomerId,
    required this.groomerName,
  });

  // Helper methods
  String get ratingText {
    switch (rating) {
      case 5:
        return 'Excelent';
      case 4:
        return 'Foarte bun';
      case 3:
        return 'Bun';
      case 2:
        return 'Satisfăcător';
      case 1:
        return 'Nesatisfăcător';
      default:
        return 'Fără rating';
    }
  }

  List<String> get ratingStars {
    List<String> stars = [];
    for (int i = 1; i <= 5; i++) {
      stars.add(i <= rating ? '★' : '☆');
    }
    return stars;
  }

  // Convert from JSON
  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json['id'],
      clientId: json['clientId'],
      appointmentId: json['appointmentId'],
      petId: json['petId'],
      petName: json['petName'],
      service: json['service'],
      rating: json['rating'],
      comment: json['comment'],
      reviewDate: DateTime.parse(json['reviewDate']),
      photoUrls: List<String>.from(json['photoUrls'] ?? []),
      isVerified: json['isVerified'] ?? false,
      groomerId: json['groomerId'],
      groomerName: json['groomerName'],
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'clientId': clientId,
      'appointmentId': appointmentId,
      'petId': petId,
      'petName': petName,
      'service': service,
      'rating': rating,
      'comment': comment,
      'reviewDate': reviewDate.toIso8601String(),
      'photoUrls': photoUrls,
      'isVerified': isVerified,
      'groomerId': groomerId,
      'groomerName': groomerName,
    };
  }
}
