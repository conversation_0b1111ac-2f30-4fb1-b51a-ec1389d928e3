class AppointmentAlternative {
  final String staffId;
  final String staffName;
  final DateTime startTime;
  final DateTime endTime;
  final int? priority;
  final double? confidence;
  final String? reason;

  AppointmentAlternative({
    required this.staffId,
    required this.staffName,
    required this.startTime,
    required this.endTime,
    this.priority,
    this.confidence,
    this.reason,
  });

  factory AppointmentAlternative.fromJson(Map<String, dynamic> json) {
    return AppointmentAlternative(
      staffId: json['staffId']?.toString() ?? '',
      staffName: json['staffName'] ?? '',
      startTime: DateTime.parse(json['startTime']),
      endTime: DateTime.parse(json['endTime']),
      priority: json['priority'],
      confidence: (json['confidence'] is num)
          ? (json['confidence'] as num).toDouble()
          : null,
      reason: json['reason'],
    );
  }
}
