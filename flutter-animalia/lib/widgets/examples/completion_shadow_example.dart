import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/appointment.dart';
import '../calendar_views/appointment_block.dart';

/// Example widget demonstrating completion shadow functionality
class CompletionShadowExample extends StatelessWidget {
  const CompletionShadowExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Completion Shadow Examples'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Appointment Completion Shadow Examples',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Visual indicators show when appointments were completed early (blue overlay) or late (red overlay)',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),
            
            // Legend
            _buildLegend(),
            const SizedBox(height: 24),
            
            // Examples
            _buildExampleSection('On Time Completion', [
              _createExampleAppointment(
                'Rex - Tuns', 
                DateTime.now().subtract(const Duration(hours: 2)),
                DateTime.now().subtract(const Duration(hours: 1)),
                DateTime.now().subtract(const Duration(hours: 1)), // Completed exactly on time
              ),
            ]),
            
            _buildExampleSection('Early Completion', [
              _createExampleAppointment(
                'Bella - Baie', 
                DateTime.now().subtract(const Duration(hours: 3)),
                DateTime.now().subtract(const Duration(hours: 2)),
                DateTime.now().subtract(const Duration(hours: 2, minutes: 20)), // 20 minutes early
              ),
              _createExampleAppointment(
                'Max - Tuns + Baie', 
                DateTime.now().subtract(const Duration(hours: 4)),
                DateTime.now().subtract(const Duration(hours: 2, minutes: 30)),
                DateTime.now().subtract(const Duration(hours: 3, minutes: 15)), // 45 minutes early
              ),
            ]),
            
            _buildExampleSection('Late Completion', [
              _createExampleAppointment(
                'Luna - Tuns', 
                DateTime.now().subtract(const Duration(hours: 3)),
                DateTime.now().subtract(const Duration(hours: 2)),
                DateTime.now().subtract(const Duration(hours: 1, minutes: 45)), // 15 minutes late
              ),
              _createExampleAppointment(
                'Charlie - Baie + Tuns', 
                DateTime.now().subtract(const Duration(hours: 4)),
                DateTime.now().subtract(const Duration(hours: 2, minutes: 30)),
                DateTime.now().subtract(const Duration(hours: 1, minutes: 30)), // 1 hour late
              ),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildLegend() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Legend:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Container(
                width: 20,
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 8),
              const Text('Early completion (finished before scheduled end time)'),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Container(
                width: 20,
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 8),
              const Text('Late completion (finished after scheduled end time)'),
            ],
          ),
          const SizedBox(height: 8),
          const Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 16),
              SizedBox(width: 8),
              Text('On-time completion (no overlay)'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExampleSection(String title, List<Appointment> appointments) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...appointments.map((appointment) => Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Row(
            children: [
              // Appointment block
              SizedBox(
                width: 200,
                height: 80,
                child: AppointmentBlock(
                  appointment: appointment,
                  height: 80,
                  onTap: () {},
                ),
              ),
              const SizedBox(width: 16),
              // Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      appointment.service,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Programat: ${DateFormat('HH:mm').format(appointment.startTime)} - ${DateFormat('HH:mm').format(appointment.endTime)}',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                    if (appointment.completedAt != null) ...[
                      Text(
                        'Finalizat: ${DateFormat('HH:mm').format(appointment.completedAt!)}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                      if (appointment.completionTimeDifferenceText.isNotEmpty)
                        Text(
                          appointment.completionTimeDifferenceText,
                          style: TextStyle(
                            color: appointment.completionTimeDifferenceMinutes > 0 
                                ? Colors.red.shade600 
                                : appointment.completionTimeDifferenceMinutes < 0
                                    ? Colors.blue.shade600
                                    : Colors.green.shade600,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        )),
        const SizedBox(height: 16),
      ],
    );
  }

  Appointment _createExampleAppointment(
    String service,
    DateTime startTime,
    DateTime endTime,
    DateTime? completedAt,
  ) {
    return Appointment(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      clientId: 'client-1',
      clientName: 'Client Test',
      clientPhone: '+40123456789',
      petId: 'pet-1',
      petName: service.split(' - ').first,
      petSpecies: 'Câine',
      service: service.split(' - ').last,
      startTime: startTime,
      endTime: endTime,
      status: completedAt != null ? 'completed' : 'scheduled',
      isPaid: true,
      completedAt: completedAt,
      actualDuration: completedAt != null 
          ? completedAt.difference(startTime).inMinutes 
          : null,
    );
  }
}
