import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../config/theme/app_theme.dart';
import '../../utils/formatters/phone_number_utils.dart';

/// Standardized form field widget with consistent styling and validation
class StandardFormField extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final int maxLines;
  final int? maxLength;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final VoidCallback? onTap;
  final Function(String)? onChanged;
  final Function(String)? onFieldSubmitted;
  final FocusNode? focusNode;
  final TextCapitalization textCapitalization;
  final String? initialValue;
  final bool filled;
  final Color? fillColor;
  final bool isRequired;
  final StandardFormFieldType type;

  const StandardFormField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.keyboardType,
    this.inputFormatters,
    this.maxLines = 1,
    this.maxLength,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.onTap,
    this.onChanged,
    this.onFieldSubmitted,
    this.focusNode,
    this.textCapitalization = TextCapitalization.sentences,
    this.initialValue,
    this.filled = true,
    this.fillColor,
    this.isRequired = false,
    this.type = StandardFormFieldType.text,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      initialValue: initialValue,
      decoration: _buildInputDecoration(context),
      validator: _buildValidator(),
      keyboardType: _getKeyboardType(),
      inputFormatters: _getInputFormatters(),
      maxLines: maxLines,
      maxLength: maxLength,
      obscureText: obscureText,
      enabled: enabled,
      readOnly: readOnly,
      onTap: onTap,
      onChanged: onChanged,
      onFieldSubmitted: onFieldSubmitted,
      focusNode: focusNode,
      textCapitalization: textCapitalization,
    );
  }

  InputDecoration _buildInputDecoration(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveFillColor =
        fillColor ?? theme.inputDecorationTheme.fillColor ?? theme.colorScheme.surface;
    
    return InputDecoration(
      labelText: _buildLabelText(),
      hintText: hintText,
      prefixIcon: prefixIcon != null
          ? Icon(prefixIcon, color: theme.colorScheme.primary)
          : null,
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: theme.colorScheme.surface,
      border: OutlineInputBorder(
        borderRadius: AppDimensions.borderRadiusMediumAll,
        borderSide: BorderSide(color: theme.colorScheme.outline),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: AppDimensions.borderRadiusMediumAll,
        borderSide: BorderSide(color: theme.colorScheme.outline),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: AppDimensions.borderRadiusMediumAll,
        borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
      ),
      // Add proper text styling based on theme
      labelStyle: TextStyle(color: theme.colorScheme.onSurface),
      hintStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
      errorBorder: OutlineInputBorder(
        borderRadius: AppDimensions.borderRadiusMediumAll,
        borderSide: BorderSide(color: theme.colorScheme.error),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: AppDimensions.borderRadiusMediumAll,
        borderSide: BorderSide(color: theme.colorScheme.error, width: 2),
      ),
      contentPadding: AppDimensions.paddingStandard,
    );
  }

  String? _buildLabelText() {
    if (labelText == null) return null;
    return isRequired ? '$labelText *' : labelText;
  }

  String? Function(String?)? _buildValidator() {
    if (validator != null) return validator;
    
    // Default validators based on type
    switch (type) {
      case StandardFormFieldType.email:
        return _emailValidator;
      case StandardFormFieldType.phone:
        return _phoneValidator;
      case StandardFormFieldType.required:
        return _requiredValidator;
      case StandardFormFieldType.text:
      default:
        return isRequired ? _requiredValidator : null;
    }
  }

  TextInputType? _getKeyboardType() {
    if (keyboardType != null) return keyboardType;
    
    switch (type) {
      case StandardFormFieldType.email:
        return TextInputType.emailAddress;
      case StandardFormFieldType.phone:
        return TextInputType.phone;
      case StandardFormFieldType.number:
        return TextInputType.number;
      case StandardFormFieldType.text:
      case StandardFormFieldType.required:
      default:
        return TextInputType.text;
    }
  }

  List<TextInputFormatter>? _getInputFormatters() {
    if (inputFormatters != null) return inputFormatters;
    
    switch (type) {
      case StandardFormFieldType.phone:
        return [PhoneNumberFormatter()];
      case StandardFormFieldType.number:
        return [FilteringTextInputFormatter.digitsOnly];
      case StandardFormFieldType.email:
      case StandardFormFieldType.text:
      case StandardFormFieldType.required:
      default:
        return null;
    }
  }

  // Validators
  String? _requiredValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppStrings.fieldRequired;
    }
    return null;
  }

  String? _emailValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return isRequired ? AppStrings.fieldRequired : null;
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value.trim())) {
      return AppStrings.invalidEmailFormat;
    }
    return null;
  }

  String? _phoneValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return isRequired ? AppStrings.fieldRequired : null;
    }
    
    if (!PhoneNumberUtils.isValidRomanianMobile(value)) {
      return AppStrings.invalidPhoneFormat;
    }
    return null;
  }
}

/// Phone number formatter for Romanian numbers
class PhoneNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Don't format if user is deleting
    if (newValue.text.length < oldValue.text.length) {
      return newValue;
    }

    // Don't format if text is empty
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // Apply formatting
    final formatted = PhoneNumberUtils.formatForInput(newValue.text);

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}

/// Types of standard form fields with predefined behavior
enum StandardFormFieldType {
  text,
  email,
  phone,
  number,
  required,
}

/// Factory constructors for common form field types
extension StandardFormFieldFactory on StandardFormField {
  /// Create a name input field
  static StandardFormField name({
    Key? key,
    TextEditingController? controller,
    String? labelText,
    String? hintText,
    String? Function(String?)? validator,
    Function(String)? onChanged,
    bool isRequired = true,
  }) {
    return StandardFormField(
      key: key,
      controller: controller,
      labelText: labelText ?? AppStrings.clientName,
      hintText: hintText,
      prefixIcon: Icons.person,
      validator: validator,
      onChanged: onChanged,
      isRequired: isRequired,
      type: StandardFormFieldType.required,
      textCapitalization: TextCapitalization.words,
    );
  }

  /// Create an email input field
  static StandardFormField email({
    Key? key,
    TextEditingController? controller,
    String? labelText,
    String? hintText,
    String? Function(String?)? validator,
    Function(String)? onChanged,
    bool isRequired = true,
  }) {
    return StandardFormField(
      key: key,
      controller: controller,
      labelText: labelText ?? AppStrings.email,
      hintText: hintText ?? '<EMAIL>',
      prefixIcon: Icons.email,
      validator: validator,
      onChanged: onChanged,
      isRequired: isRequired,
      type: StandardFormFieldType.email,
    );
  }

  /// Create a phone input field
  static StandardFormField phone({
    Key? key,
    TextEditingController? controller,
    String? labelText,
    String? hintText,
    String? Function(String?)? validator,
    Function(String)? onChanged,
    bool isRequired = true,
  }) {
    return StandardFormField(
      key: key,
      controller: controller,
      labelText: labelText ?? AppStrings.phoneNumber,
      hintText: hintText ?? '+40 728 626 399',
      prefixIcon: Icons.phone,
      validator: validator,
      onChanged: onChanged,
      isRequired: isRequired,
      type: StandardFormFieldType.phone,
    );
  }

  /// Create a notes/description field
  static StandardFormField notes({
    Key? key,
    TextEditingController? controller,
    String? labelText,
    String? hintText,
    String? Function(String?)? validator,
    Function(String)? onChanged,
    int maxLines = 3,
    bool isRequired = false,
  }) {
    return StandardFormField(
      key: key,
      controller: controller,
      labelText: labelText ?? 'Observații',
      hintText: hintText,
      prefixIcon: Icons.notes,
      validator: validator,
      onChanged: onChanged,
      maxLines: maxLines,
      isRequired: isRequired,
      textCapitalization: TextCapitalization.sentences,
    );
  }
}
