import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../models/working_hours_settings.dart';
import '../../services/staff_validation_error_service.dart';
import '../../services/ui_notification_service.dart';
import '../../utils/romanian_holidays.dart';
import '../reason_selector/smart_reason_selector.dart';
import '../time_pickers/modern_time_picker.dart';
import 'gesture_day_toggle.dart';
import 'schedule_templates.dart';

/// Reusable schedule management widget that can be used for both salon and staff schedules
class ScheduleManagementWidget extends StatefulWidget {
  final WorkingHoursSettings? currentSettings;
  final bool isLoading;
  final bool isSaving;
  final String? error;
  final Function() onRefresh;
  final Function(Map<String, DaySchedule>) onTemplateSelected;
  final Function(String, DaySchedule) onDayScheduleUpdate;
  final Function(CustomClosure) onCustomClosureAdd;
  final Function(DateTime) onCustomClosureRemove;
  final String title;
  final bool showTemplates;
  final bool showHolidays;
  final bool showCustomClosures;
  final bool showCurrentStatus;
  final bool showSaveButton;
  final bool hasUnsavedChanges;
  final Function()? onSave;
  final WorkingHoursSettings? businessHours; // For displaying business hours context

  const ScheduleManagementWidget({
    Key? key,
    required this.currentSettings,
    required this.isLoading,
    required this.isSaving,
    this.error,
    required this.onRefresh,
    required this.onTemplateSelected,
    required this.onDayScheduleUpdate,
    required this.onCustomClosureAdd,
    required this.onCustomClosureRemove,
    this.title = 'Program de Lucru',
    this.showTemplates = true,
    this.showHolidays = true,
    this.showCustomClosures = true,
    this.showCurrentStatus = true,
    this.showSaveButton = false,
    this.hasUnsavedChanges = false,
    this.onSave,
    this.businessHours,
  }) : super(key: key);

  @override
  State<ScheduleManagementWidget> createState() => _ScheduleManagementWidgetState();
}

class _ScheduleManagementWidgetState extends State<ScheduleManagementWidget> {
  final List<String> _weekDays = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
    'sunday'
  ];
  final Map<String, String> _dayNames = {
    'monday': 'Luni',
    'tuesday': 'Marți',
    'wednesday': 'Miercuri',
    'thursday': 'Joi',
    'friday': 'Vineri',
    'saturday': 'Sâmbătă',
    'sunday': 'Duminică',
  };



  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return Center(
        child: CircularProgressIndicator(color: Theme.of(context).colorScheme.onSurface),
      );
    }

    if (widget.error != null) {
      return _buildErrorView();
    }

    if (widget.currentSettings == null) {
      return _buildEmptyState();
    }

    return Stack(
      children: [
        SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current status card
              if (widget.showCurrentStatus) ...[
                // _buildCurrentStatusCard(),
                // SizedBox(height: 24),
              ],

              // Schedule templates section
              if (widget.showTemplates) ...[
                SizedBox(height: 16),
                _buildScheduleTemplatesCard(),
                SizedBox(height: 24),
              ],

              // Weekly schedule section
              SizedBox(height: 16),
              _buildWeeklyScheduleCard(),
              SizedBox(height: 24),

              // Holidays section
              if (widget.showHolidays) ...[
                SizedBox(height: 16),
                _buildHolidaysCard(),
                SizedBox(height: 24),
              ],



              // Extra space for save button
              if (widget.showSaveButton) SizedBox(height: 80),
            ],
          ),
        ),

        // Dynamic save button
        if (widget.showSaveButton && widget.hasUnsavedChanges && widget.onSave != null)
          _buildSaveButton(),
      ],
    );
  }

  /// Build schedule templates card
  Widget _buildScheduleTemplatesCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: ScheduleTemplates(
          onTemplateSelected: widget.onTemplateSelected,
        ),
      ),
    );
  }

  Widget _buildWeeklyScheduleCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Program Săptămânal',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                if (widget.isSaving)
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              'Glisează pentru a activa/dezactiva zilele sau apasă pentru a edita',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 16),
            ..._weekDays.map((day) => _buildDayScheduleRow(day)).toList(),
          ],
        ),
      ),
    );
  }

  /// Build modern gesture-based day schedule row
  Widget _buildDayScheduleRow(String dayOfWeek) {
    final schedule = widget.currentSettings?.getScheduleForDay(dayOfWeek);
    final dayName = _dayNames[dayOfWeek] ?? dayOfWeek;

    return GestureDayToggle(
      dayName: dayName,
      isWorkingDay: schedule?.isWorkingDay ?? false,
      startTime: schedule?.startTime,
      endTime: schedule?.endTime,
      onToggle: (isWorking) => _toggleWorkingDay(dayOfWeek, isWorking),
      onTap: () => _editDaySchedule(dayOfWeek),
    );
  }

  /// Toggle working day status
  Future<void> _toggleWorkingDay(String dayOfWeek, bool isWorking) async {
    final currentSchedule = widget.currentSettings?.getScheduleForDay(dayOfWeek);

    final newSchedule = DaySchedule(
      startTime: isWorking ? (currentSchedule?.startTime ?? '09:00') : null,
      endTime: isWorking ? (currentSchedule?.endTime ?? '17:00') : null,
      isWorkingDay: isWorking,
      breakStart: isWorking ? currentSchedule?.breakStart : null,
      breakEnd: isWorking ? currentSchedule?.breakEnd : null,
    );

    widget.onDayScheduleUpdate(dayOfWeek, newSchedule);
  }



  /// Edit day schedule
  Future<void> _editDaySchedule(String dayOfWeek) async {
    final schedule = widget.currentSettings?.getScheduleForDay(dayOfWeek);
    final dayName = _dayNames[dayOfWeek] ?? dayOfWeek;

    final result = await showDialog<DaySchedule>(
      context: context,
      builder: (context) => _DayScheduleEditDialog(
        dayName: dayName,
        schedule: schedule,
      ),
    );

    if (result != null) {
      widget.onDayScheduleUpdate(dayOfWeek, result);
    }
  }

  /// Build holidays card
  Widget _buildHolidaysCard() {
    final holidays = widget.currentSettings?.holidays ?? [];
    final upcomingHolidays = RomanianHolidays.getUpcomingHolidays(DateTime.now());

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sărbători și Zile Libere',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 16),
            if (upcomingHolidays.isEmpty)
              Text(
                'Nu sunt sărbători în următoarele 30 de zile',
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontStyle: FontStyle.italic,
                ),
              )
            else
              ...upcomingHolidays.take(5).map((holiday) => _buildHolidayItem(holiday)).toList(),
          ],
        ),
      ),
    );
  }

  /// Build holiday item
  Widget _buildHolidayItem(Holiday holiday) {
    final isToday = DateTime.now().day == holiday.date.day &&
        DateTime.now().month == holiday.date.month &&
        DateTime.now().year == holiday.date.year;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isToday ? Theme.of(context).colorScheme.primary.withOpacity(0.1) : Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
        border: isToday ? Border.all(color: Theme.of(context).colorScheme.onSurface, width: 2) : null,
      ),
      child: Row(
        children: [
          Icon(
            _getHolidayIcon(holiday.type),
            color: isToday ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.onSurfaceVariant,
            size: 20,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  holiday.name,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                    color: isToday ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                Text(
                  DateFormat('dd MMMM yyyy', 'ro').format(holiday.date),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          if (holiday.isWorkingDay)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Lucrăm',
                style: TextStyle(
                  fontSize: 10,
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Get holiday icon based on type
  IconData _getHolidayIcon(HolidayType type) {
    switch (type) {
      case HolidayType.religious:
        return Icons.church;
      case HolidayType.national:
        return Icons.flag;
      case HolidayType.legal:
        return Icons.event;
    }
  }

  /// Build custom closures card
  Widget _buildCustomClosuresCard() {
    final customClosures = widget.currentSettings?.customClosures ?? [];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Închideri Personalizate',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addCustomClosure,
                  icon:  Icon(Icons.add),
                  label: Text('Adaugă'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            if (customClosures.isEmpty)
              Text(
                'Nu sunt închideri personalizate programate',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              )
            else
              ...customClosures.map((closure) => _buildCustomClosureItem(closure)).toList(),
          ],
        ),
      ),
    );
  }

  /// Build custom closure item
  Widget _buildCustomClosureItem(CustomClosure closure) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.event_busy,
            color: Theme.of(context).colorScheme.secondary,
            size: 20,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  closure.reason,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  _formatItemRange(closure),
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                if (closure.description != null && closure.description!.isNotEmpty)
                  Text(
                    closure.description!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _removeCustomClosure(closure),
            icon: Icon(Icons.delete, color: Theme.of(context).colorScheme.error, size: 20),
            tooltip: 'Șterge închiderea',
          ),
        ],
      ),
    );
  }

  /// Add custom closure
  Future<void> _addCustomClosure() async {
    final result = await showDialog<CustomClosure>(
      context: context,
      builder: (context) => const _CustomClosureAddDialog(),
    );

    if (result != null) {
      widget.onCustomClosureAdd(result);
    }
  }

  /// Remove custom closure
  Future<void> _removeCustomClosure(CustomClosure closure) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Confirmare ștergere'),
        content: Text('Sigur doriți să ștergeți închiderea din ${_formatItemRange(closure)}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Anulează'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Theme.of(context).colorScheme.error),
            child: Text('Șterge', style: TextStyle(color: Theme.of(context).colorScheme.onError)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      widget.onCustomClosureRemove(closure.startDate);
    }
  }

  /// Format date range for custom closure
  String _formatItemRange(CustomClosure closure) {
    final start = DateFormat('dd MMM yyyy', 'ro').format(closure.startDate);
    final end = DateFormat('dd MMM yyyy', 'ro').format(closure.endDate);
    return closure.startDate == closure.endDate ? start : '$start - $end';
  }

  /// Build dynamic save button
  Widget _buildSaveButton() {
    return Positioned(
      bottom: 16,
      left: 16,
      right: 16,
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: widget.isSaving ? null : widget.onSave,
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 0,
          ),
          child: widget.isSaving
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                    SizedBox(width: 12),
                    Text(
                      'Se salvează...',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                )
              : Text(
                  'Salvează modificările',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ),
    );
  }

  /// Build error view with enhanced validation error display
  Widget _buildErrorView() {
    final parsedError = widget.error != null ? StaffValidationErrorService.parseValidationError(widget.error!) : 'Eroare necunoscută';
    final isValidationError = StaffValidationErrorService.shouldShowBusinessHoursContext(widget.error);
    final errorTitle = StaffValidationErrorService.getErrorTitle(widget.error);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isValidationError ? Icons.schedule : Icons.error_outline,
              size: 64,
              color: isValidationError ? Colors.orange : Colors.red,
            ),
            SizedBox(height: 16),
            Text(
              errorTitle,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: (isValidationError ? Colors.orange : Colors.red).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: (isValidationError ? Colors.orange : Colors.red).withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                parsedError,
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                textAlign: TextAlign.left,
              ),
            ),

            // Show business hours context for validation errors
            if (isValidationError && widget.businessHours != null) ...[
              SizedBox(height: 16),
              _buildBusinessHoursContext(),
            ],

            SizedBox(height: 24),
            ElevatedButton(
              onPressed: widget.onRefresh,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                
              ),
              child: Text('Încearcă din nou'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build business hours context display
  Widget _buildBusinessHoursContext() {
    if (widget.businessHours == null) return SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.business,
                color: Theme.of(context).colorScheme.onSurface,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Program salon',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          ..._weekDays.map((day) {
            final schedule = widget.businessHours!.getScheduleForDay(day);
            final dayName = _dayNames[day] ?? day;

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Row(
                children: [
                  SizedBox(
                    width: 80,
                    child: Text(
                      dayName,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Text(
                    schedule?.isWorkingDay == true
                        ? '${schedule!.startTime} - ${schedule.endTime}'
                        : 'Închis',
                    style: TextStyle(
                      fontSize: 14,
                      color: schedule?.isWorkingDay == true ? Theme.of(context).colorScheme.onSurface : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.schedule,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            SizedBox(height: 16),
            Text(
              widget.title,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Nu s-au găsit setări pentru program.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: widget.onRefresh,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                
              ),
              child: Text('Reîncarcă'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Enhanced dialog for editing day schedule with modern UX
class _DayScheduleEditDialog extends StatefulWidget {
  final String dayName;
  final DaySchedule? schedule;

  const _DayScheduleEditDialog({
    required this.dayName,
    this.schedule,
  });

  @override
  State<_DayScheduleEditDialog> createState() => _DayScheduleEditDialogState();
}

class _DayScheduleEditDialogState extends State<_DayScheduleEditDialog>
    with TickerProviderStateMixin {
  late bool _isWorkingDay;
  String? _startTime;
  String? _endTime;
  String? _breakStart;
  String? _breakEnd;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  /// Normalize time format by removing seconds if present (HH:MM:SS -> HH:MM)
  String _normalizeTimeFormat(String time) {
    if (time.contains(':')) {
      final parts = time.split(':');
      if (parts.length >= 2) {
        return '${parts[0]}:${parts[1]}';
      }
    }
    return time;
  }

  @override
  void initState() {
    super.initState();
    _isWorkingDay = widget.schedule?.isWorkingDay ?? true;
    _startTime = widget.schedule?.startTime != null ? _normalizeTimeFormat(widget.schedule!.startTime!) : (_isWorkingDay ? '09:00' : null);
    _endTime = widget.schedule?.endTime != null ? _normalizeTimeFormat(widget.schedule!.endTime!) : (_isWorkingDay ? '17:00' : null);
    _breakStart = widget.schedule?.breakStart != null ? _normalizeTimeFormat(widget.schedule!.breakStart!) : null;
    _breakEnd = widget.schedule?.breakEnd != null ? _normalizeTimeFormat(widget.schedule!.breakEnd!) : null;

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                constraints: const BoxConstraints(
                  maxWidth: 400,
                  maxHeight: 600, // Add max height constraint
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.onSurface,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.schedule,
                              color: Theme.of(context).colorScheme.onPrimary,
                              size: 20,
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Editează ${widget.dayName}',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).colorScheme.onSurface,
                                  ),
                                ),
                                Text(
                                  'Configurează programul pentru această zi',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Content
                    Flexible(
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.all(24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                          // Working day toggle
                          Row(
                            children: [
                              Text(
                                'Zi lucrătoare',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const Spacer(),
                              Switch(
                                value: _isWorkingDay,
                                onChanged: (value) {
                                  setState(() {
                                    _isWorkingDay = value;
                                    if (!value) {
                                      _startTime = null;
                                      _endTime = null;
                                      _breakStart = null;
                                      _breakEnd = null;
                                    } else {
                                      _startTime ??= '09:00';
                                      _endTime ??= '17:00';
                                    }
                                  });
                                },
                                activeColor: Theme.of(context).colorScheme.primary,
                              ),
                            ],
                          ),

                          if (_isWorkingDay) ...[
                            SizedBox(height: 24),
                            Text(
                              'Ore de lucru',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                            SizedBox(height: 12),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildTimeField(
                                    label: 'Început',
                                    value: _startTime,
                                    onChanged: (time) {
                                      setState(() {
                                        _startTime = time;
                                        // If start time is set but end time is null, set a default end time
                                        if (time != null && _endTime == null) {
                                          _endTime = '17:00';
                                        }
                                      });
                                    },
                                  ),
                                ),
                                SizedBox(width: 16),
                                Expanded(
                                  child: _buildTimeField(
                                    label: 'Sfârșit',
                                    value: _endTime,
                                    onChanged: (time) {
                                      setState(() {
                                        _endTime = time;
                                        // If end time is set but start time is null, set a default start time
                                        if (time != null && _startTime == null) {
                                          _startTime = '09:00';
                                        }
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 16),
                            Text(
                              'Pauză',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                            SizedBox(height: 12),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildTimeField(
                                    label: 'Început pauză',
                                    value: _breakStart,
                                    onChanged: (time) => setState(() => _breakStart = time),
                                    isOptional: true,
                                    showOptionalHint: false,
                                  ),
                                ),
                                SizedBox(width: 16),
                                Expanded(
                                  child: _buildTimeField(
                                    label: 'Sfârșit pauză',
                                    value: _breakEnd,
                                    onChanged: (time) => setState(() => _breakEnd = time),
                                    isOptional: true,
                                    showOptionalHint: false,
                                  ),
                                ),
                              ],
                            ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),

                    // Actions
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceVariant,
                        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(20)),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('Anulează'),
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _saveSchedule,
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: const Text('Salvează'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTimeField({
    required String label,
    required String? value,
    required Function(String?) onChanged,
    bool isOptional = false,
    bool showOptionalHint = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label + ((isOptional && showOptionalHint) ? ' (opțional)' : ''),
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 4),
        ModernTimePicker(
          label: label,
          initialTime: value ?? (isOptional ? null : '09:00'),
          onTimeChanged: onChanged,
        ),
      ],
    );
  }

  void _saveSchedule() {
    // Normalize time formats (remove seconds if present) - do this outside the if block
    String? normalizedStartTime;
    String? normalizedEndTime;
    String? normalizedBreakStart;
    String? normalizedBreakEnd;

    // Validate times if it's a working day
    if (_isWorkingDay) {
      // Check if required times are provided
      if (_startTime == null || _endTime == null) {
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Vă rugăm să selectați ora de început și sfârșit'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Normalize time formats (remove seconds if present)
      normalizedStartTime = _normalizeTimeFormat(_startTime!);
      normalizedEndTime = _normalizeTimeFormat(_endTime!);
      normalizedBreakStart = _breakStart != null ? _normalizeTimeFormat(_breakStart!) : null;
      normalizedBreakEnd = _breakEnd != null ? _normalizeTimeFormat(_breakEnd!) : null;

      // Debug logging

      // Validate time formats
      if (!RomanianHolidays.isValidTimeFormat(normalizedStartTime) ||
          !RomanianHolidays.isValidTimeFormat(normalizedEndTime)) {
        UINotificationService.showWarning(
          context: context,
          title: 'Format oră invalid',
          message: 'Vă rugăm să folosiți formatul HH:MM (ex: 09:00, 17:30)',
          actionLabel: 'Înțeles',
        );
        return;
      }

      // Validate break times if provided
      if (normalizedBreakStart != null && normalizedBreakEnd != null) {
        if (!RomanianHolidays.isValidTimeFormat(normalizedBreakStart) ||
            !RomanianHolidays.isValidTimeFormat(normalizedBreakEnd)) {
          UINotificationService.showWarning(
            context: context,
            title: 'Format oră pauză invalid',
            message: 'Pentru pauze, folosiți formatul HH:MM (ex: 12:00, 13:00)',
            actionLabel: 'Înțeles',
          );
          return;
        }

        // Validate break times are within working hours
        final startMinutes = RomanianHolidays.timeToMinutes(normalizedStartTime);
        final endMinutes = RomanianHolidays.timeToMinutes(normalizedEndTime);
        final breakStartMinutes = RomanianHolidays.timeToMinutes(normalizedBreakStart);
        final breakEndMinutes = RomanianHolidays.timeToMinutes(normalizedBreakEnd);

        if (breakStartMinutes < startMinutes || breakEndMinutes > endMinutes) {
          showTopSnackBar(context, 
            SnackBar(
              content: Text('Pauza trebuie să fie în intervalul orelor de lucru'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        if (breakStartMinutes >= breakEndMinutes) {
          showTopSnackBar(context, 
            SnackBar(
              content: Text('Ora de început a pauzei trebuie să fie înainte de ora de sfârșit'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      }

      // Validate start time is before end time
      final startMinutes = RomanianHolidays.timeToMinutes(normalizedStartTime);
      final endMinutes = RomanianHolidays.timeToMinutes(normalizedEndTime);

      if (startMinutes >= endMinutes) {
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Ora de început trebuie să fie înainte de ora de sfârșit'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }

    final schedule = DaySchedule(
      isWorkingDay: _isWorkingDay,
      startTime: _isWorkingDay ? normalizedStartTime : null,
      endTime: _isWorkingDay ? normalizedEndTime : null,
      breakStart: _isWorkingDay ? normalizedBreakStart : null,
      breakEnd: _isWorkingDay ? normalizedBreakEnd : null,
    );

    Navigator.of(context).pop(schedule);
  }
}

/// Enhanced dialog for adding custom closure with smart reason selector - matching salon style
class _CustomClosureAddDialog extends StatefulWidget {
  const _CustomClosureAddDialog();

  @override
  State<_CustomClosureAddDialog> createState() => _CustomClosureAddDialogState();
}

class _CustomClosureAddDialogState extends State<_CustomClosureAddDialog>
    with TickerProviderStateMixin {
  String? _selectedReason;
  final _descriptionController = TextEditingController();
  DateTimeRange _selectedRange =
      DateTimeRange(start: DateTime.now(), end: DateTime.now());

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.event_busy,
                              color: Theme.of(context).colorScheme.onPrimary,
                              size: 20,
                            ),
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Închidere personalizată',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  'Adaugă o zi de închidere specială',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Content
                    Flexible(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Smart reason selector
                            SmartReasonSelector(
                              label: 'Motiv închidere',
                              onReasonChanged: (reason) {
                                _selectedReason = reason;
                              },
                            ),

                            SizedBox(height: 24),

                            // Date selector
                            _buildDateRangeSelector(),

                            SizedBox(height: 24),

                            // Description field
                            _buildDescriptionField(),
                          ],
                        ),
                      ),
                    ),

                    // Actions
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceVariant,
                        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(20)),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: Text(
                                'Anulează',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _saveClosure,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                
                                padding: const EdgeInsets.symmetric(horizontal: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                'Adaugă închiderea',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDateRangeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Perioada închiderii',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12),
        GestureDetector(
          onTap: _selectDateRange,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.calendar_today,
                    color: Colors.orange,
                    size: 20,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Perioada selectată',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        _formatRange(),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.grey,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Descriere suplimentară (opțional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12),
        TextFormField(
          controller: _descriptionController,
          textCapitalization: TextCapitalization.sentences,
          decoration: InputDecoration(
            hintText: 'Adaugă detalii suplimentare...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.orange),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
          maxLines: 3,
          maxLength: 200,
        ),
      ],
    );
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      initialDateRange: _selectedRange,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ro'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(primary: Colors.orange),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedRange = picked;
      });
    }
  }

  String _formatRange() {
    final start = DateFormat('dd MMM yyyy', 'ro').format(_selectedRange.start);
    final end = DateFormat('dd MMM yyyy', 'ro').format(_selectedRange.end);
    return _selectedRange.start == _selectedRange.end ? start : '$start - $end';
  }

  String _formatItemRange(CustomClosure closure) {
    final start = DateFormat('dd MMM yyyy', 'ro').format(closure.startDate);
    final end = DateFormat('dd MMM yyyy', 'ro').format(closure.endDate);
    return closure.startDate == closure.endDate ? start : '$start - $end';
  }

  void _saveClosure() {
    if (_selectedReason == null || _selectedReason!.trim().isEmpty) {
      showTopSnackBar(context, 
        SnackBar(
          content: Text('Te rugăm să selectezi un motiv pentru închidere'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final closure = CustomClosure(
      reason: _selectedReason!.trim(),
      startDate: _selectedRange.start,
      endDate: _selectedRange.end,
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
    );

    Navigator.of(context).pop(closure);
  }
}
