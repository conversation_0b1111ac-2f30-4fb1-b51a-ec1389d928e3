import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../config/theme/app_theme.dart';
import '../providers/auth_provider.dart';
import '../providers/calendar_provider.dart';
import '../providers/client_provider.dart';
import '../providers/role_provider.dart';
import '../providers/subscription_provider.dart';
import '../screens/auth/login_screen.dart';
import '../screens/main_layout.dart';
import '../services/subscription/revenue_cat_subscription_service.dart';

/// Authentication wrapper that handles routing based on authentication state
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isInitializing = true;
  String? _initializationError;
  bool? _needsSubscriptionSelection;

  @override
  void initState() {
    super.initState();
    // Initialize authentication state when the widget is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeAuth();
    });
  }


  Future<void> _initializeAuth() async {
    try {
      DebugLogger.logVerbose('🔄 AuthWrapper: Starting initialization...');
      DebugLogger.logVerbose('🔍 AuthWrapper: Device type - ${_getDeviceType()}');

      final authProvider = context.read<AuthProvider>();
      final roleProvider = context.read<RoleProvider>();

      DebugLogger.logVerbose('🔄 AuthWrapper: Providers obtained');

      // Set role provider reference in auth provider for future role initialization
      authProvider.setRoleProvider(roleProvider);

      // Register other providers for cleanup during logout
      try {
        final clientProvider = context.read<ClientProvider>();
        final subscriptionProvider = context.read<SubscriptionProvider>();
        final calendarProvider = context.read<CalendarProvider>();

        authProvider.addProviderToCleanup(clientProvider);
        authProvider.addProviderToCleanup(subscriptionProvider);
        authProvider.setCalendarProvider(calendarProvider);

        DebugLogger.logVerbose('🔄 AuthWrapper: All providers registered for cleanup');
      } catch (e) {
        DebugLogger.logVerbose('⚠️ AuthWrapper: Some providers not available for cleanup registration: $e');
      }

      DebugLogger.logVerbose('🔄 AuthWrapper: Provider references set');

      // Initialize authentication first
      DebugLogger.logVerbose('🔄 AuthWrapper: Initializing auth provider...');
      await authProvider.initialize();
      DebugLogger.logVerbose('✅ AuthWrapper: Auth provider initialized - isAuthenticated: ${authProvider.isAuthenticated}');

      // Initialize roles only if user is authenticated
      if (authProvider.isAuthenticated) {
        DebugLogger.logVerbose('🔄 AuthWrapper: User authenticated, initializing roles...');
        await roleProvider.initialize();
        DebugLogger.logVerbose('✅ AuthWrapper: Role initialization completed');
        DebugLogger.logVerbose('🔍 AuthWrapper: hasSalonAssociation: ${roleProvider.hasSalonAssociation}');
        DebugLogger.logVerbose('🔍 AuthWrapper: currentRole: ${roleProvider.currentRole}');
        DebugLogger.logVerbose('🔍 AuthWrapper: permissions: ${roleProvider.permissions}');

        // Check if user needs subscription selection
        DebugLogger.logVerbose('🔄 AuthWrapper: Checking subscription selection needs...');
        _needsSubscriptionSelection = await RevenueCatSubscriptionService.needsSubscriptionSelection();
        DebugLogger.logVerbose('🔍 AuthWrapper: needsSubscriptionSelection: $_needsSubscriptionSelection');
      } else {
        DebugLogger.logVerbose('🔍 AuthWrapper: User not authenticated, clearing roles');
        roleProvider.clear();
        _needsSubscriptionSelection = false;
      }

      // Mark initialization as complete
      if (mounted) {
        DebugLogger.logVerbose('✅ AuthWrapper: Marking initialization as complete');
        setState(() {
          _isInitializing = false;
          _initializationError = null;
        });
        DebugLogger.logVerbose('✅ AuthWrapper: State updated - initialization complete');
      } else {
        DebugLogger.logVerbose('⚠️ AuthWrapper: Widget not mounted, skipping state update');
      }
    } catch (e, stackTrace) {
      DebugLogger.logVerbose('❌ AuthWrapper: Error during initialization: $e');
      DebugLogger.logVerbose('❌ AuthWrapper: Stack trace: $stackTrace');
      if (mounted) {
        setState(() {
          _isInitializing = false;
          _initializationError = 'Initialization error: $e';
        });
      }
    }
  }

  String _getDeviceType() {
    final data = MediaQuery.of(context);
    final shortestSide = data.size.shortestSide;
    if (shortestSide < 600) {
      return 'Phone (${data.size.width.toInt()}x${data.size.height.toInt()})';
    } else {
      return 'Tablet (${data.size.width.toInt()}x${data.size.height.toInt()})';
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show initialization loading screen if still initializing
    if (_isInitializing) {
      return  Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
              ),
              SizedBox(height: 16),
              Text(
                'Inițializare aplicație...',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.taupe,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Show initialization error if present
    if (_initializationError != null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Eroare la inițializare',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  _initializationError!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.taupe,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isInitializing = true;
                    _initializationError = null;
                  });
                  _initializeAuth();
                },
                style: ElevatedButton.styleFrom(
                ),
                child: const Text('Încearcă din nou'),
              ),
            ],
          ),
        ),
      );
    }

    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        switch (authProvider.status) {
          case AuthStatus.initial:
          case AuthStatus.authenticating:
            // Show loading screen while checking authentication status
            return  Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Verificare autentificare...',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.taupe,
                      ),
                    ),
                  ],
                ),
              ),
            );

          case AuthStatus.authenticated:
            return MainLayout(key: MainLayout.globalKey);
            case AuthStatus.unauthenticated:
            // User is not authenticated, show login screen
            return const LoginScreen();

          case AuthStatus.error:
            // Show error with retry option for network issues
            return Scaffold(
              body: Center(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: AppColors.taupe,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Eroare de conectare',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.taupe,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        authProvider.error ?? 'A apărut o problemă la verificarea autentificării.',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppColors.taupe,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton(
                            onPressed: () async {
                              // Retry authentication
                              await authProvider.initialize();
                            },
                            style: ElevatedButton.styleFrom(
                            ),
                            child: const Text('Încearcă din nou'),
                          ),
                          TextButton(
                            onPressed: () async {
                              // Clear auth and go to login
                              await authProvider.signOut();
                            },
                            child: Text(
                              'Autentificare nouă',
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
        }
      },
    );
  }
}
