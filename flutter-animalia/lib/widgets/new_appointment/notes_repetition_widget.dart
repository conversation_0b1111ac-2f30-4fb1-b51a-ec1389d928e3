import 'package:flutter/material.dart';
import '../../config/theme/app_theme.dart';
import 'appointment_form_data.dart';

class NotesRepetitionWidget extends StatelessWidget {
  final AppointmentFormData formData;
  final Function(bool) onNotesVisibilityChanged;
  final Function(bool) onRepetitionVisibilityChanged;
  final Function(String) onRepetitionFrequencyChanged;
  final Function(String) onNotesChanged;

  const NotesRepetitionWidget({
    super.key,
    required this.formData,
    required this.onNotesVisibilityChanged,
    required this.onRepetitionVisibilityChanged,
    required this.onRepetitionFrequencyChanged,
    required this.onNotesChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Small + buttons row
        Row(
          children: [
            _buildSmallAddButton(
              'Observații',
              Icons.note_alt,
              formData.showNotes,
              () => onNotesVisibilityChanged(!formData.showNotes),
              context,
            ),
            // Repetition button removed
          ],
        ),

        const SizedBox(height: 16),

        // Show notes field if visible
        if (formData.showNotes) ...[
          _buildNotesField(context),
          const SizedBox(height: 16),
        ],

        // Repetition options section removed
      ],
    );
  }

  Widget _buildSmallAddButton(String label, IconData icon, bool isSelected, VoidCallback onTap, BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surfaceVariant,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isSelected ? Icons.check : Icons.add,
              size: 16,
              color: isSelected ? Colors.white : Colors.grey[600],
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: isSelected ? Colors.white : Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesField(BuildContext context) {
    return TextFormField(
      decoration:  InputDecoration(
        labelText: 'Observații',
        hintText: 'Adaugă observații pentru această programare...',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.note_alt, color: Theme.of(context).primaryColor),
      ),
      maxLines: 3,
      initialValue: formData.notes,
      onChanged: onNotesChanged,
      textCapitalization: TextCapitalization.sentences,
    );
  }


}
