import 'package:flutter/foundation.dart';

import '../../models/client.dart';
import '../../models/pet.dart';
import '../../models/user_role.dart';
import '../../providers/calendar_provider.dart';
import '../../services/appointment/calendar_service.dart';
import '../../services/staff_service.dart';
import '../../utils/formatters/phone_number_utils.dart';
import 'appointment_form_constants.dart';
import 'appointment_form_data.dart';

/// Business logic helper for appointment form
class AppointmentFormLogic {
  final CalendarProvider calendarProvider;

  AppointmentFormLogic(this.calendarProvider);

  /// Loads initial data for the form
  Future<void> loadInitialData(AppointmentFormData formData, {String? preselectedStaffId}) async {
    await Future.wait([
      _loadServices(formData, preselectedStaffId: preselectedStaffId),
      _loadClients(formData),
      _loadStaff(formData, preselectedStaffId: preselectedStaffId),
    ]);
  }

  /// Loads available services
  Future<void> _loadServices(AppointmentFormData formData, {String? preselectedStaffId}) async {
    // Load services from backend using CalendarProvider
    await calendarProvider.loadServices();
    formData.availableServices = calendarProvider.getAvailableServiceNames();

    // Feature 3: Auto-select default standard service if available and no service is selected
    if (formData.services.isEmpty && formData.availableServices.isNotEmpty) {
      const defaultServiceName = 'Serviciu Standard';
      if (formData.availableServices.contains(defaultServiceName)) {
        formData.services = [defaultServiceName];
        debugPrint('✅ Auto-selected default service: $defaultServiceName');
      } else {
        // Fallback: select the first available service
        formData.services = [formData.availableServices.first];
        debugPrint('✅ Auto-selected first available service: ${formData.availableServices.first}');
      }
    }
  }

  /// Loads available staff members
  Future<void> _loadStaff(AppointmentFormData formData, {String? preselectedStaffId}) async {
    try {
      // Get active staff from the current salon
      final response = await StaffService.getCurrentSalonStaff(activeOnly: true);

      if (response.success && response.data != null) {
        // Filter for staff with groomer roles only
        final groomerStaff = response.data!.activeStaff.where((staff) =>
          staff.groomerRole == GroomerRole.chiefGroomer ||
          staff.groomerRole == GroomerRole.groomer ||
          staff.groomerRole == GroomerRole.assistant
        ).toList();

        formData.availableStaff = groomerStaff;

        // Handle staff preselection or default selection
        if (preselectedStaffId != null) {
          // Find and preselect the specified staff member
          final preselectedStaff = formData.availableStaff
              .where((staff) => staff.id == preselectedStaffId)
              .isNotEmpty
              ? formData.availableStaff
                  .where((staff) => staff.id == preselectedStaffId)
                  .first
              : null;

          if (preselectedStaff != null) {
            formData.updateStaffData(preselectedStaff);
          } else if (formData.availableStaff.isNotEmpty) {
            // Fallback to first staff if preselected not found
            formData.updateStaffData(formData.availableStaff.first);
          }
        } else if (formData.assignedCoworker.isEmpty && formData.availableStaff.isNotEmpty) {
          // Set default staff if none selected and no preselection
          formData.updateStaffData(formData.availableStaff.first);
        }
      } else {
        formData.availableStaff = [];
      }
    } catch (e) {
      formData.availableStaff = [];
    }
  }

  /// Loads available clients
  Future<void> _loadClients(AppointmentFormData formData) async {
    try {
      // Get all clients from HTTP service via CalendarProvider
      final clients = await calendarProvider.calendarService.getAllClients();
      formData.availableClients = clients;
    } catch (e) {
      formData.availableClients = [];
    }

    // Don't auto-select the first client - let user choose
    // Reset client data to empty state
    formData.clientId = '';
    formData.clientName = '';
    formData.clientPhone = '';
    formData.clientPets = [];
  }

  /// Loads pets for a specific client
  Future<void> loadPetsForClient(AppointmentFormData formData, String clientId) async {
    try {
      debugPrint('🔄 AppointmentFormLogic: Loading pets for client: $clientId');

      // Clear pets first to show loading state
      formData.clientPets = [];

      // Use force refresh to bypass any caching issues
      final pets = await calendarProvider.refreshPetsForClient(clientId);
      formData.clientPets = pets;
      formData.savedClientPets = [...pets];

      debugPrint('✅ AppointmentFormLogic: Loaded ${pets.length} pets for client $clientId');

      if (pets.isNotEmpty) {
        // Auto-select first pet if available
        formData.updatePetData(pets.first);
        debugPrint('🐕 AppointmentFormLogic: Auto-selected pet: ${pets.first.name}');
      } else {
        debugPrint('⚠️ AppointmentFormLogic: No pets found for client $clientId');
      }
    } catch (e) {
      debugPrint('❌ AppointmentFormLogic: Error loading pets for client $clientId: $e');
      formData.clientPets = [];
    }
  }

  /// Updates end time based on selected services
  Future<void> updateEndTimeBasedOnServices(AppointmentFormData formData) async {
    final serviceDurations = await calendarProvider.getServiceDurations();
    final totalDuration = formData.getTotalDuration(serviceDurations);

    // Calculate exact end time based on services - no rounding
    final newEndTime = formData.startTime.add(Duration(minutes: totalDuration));

    // Ensure the end time doesn't go beyond 23:59
    if (newEndTime.hour >= 24) {
      // If it goes to the next day, cap it at 23:45
      formData.endTime = DateTime(
        formData.startTime.year,
        formData.startTime.month,
        formData.startTime.day,
        23,
        45,
      );
    } else {
      formData.endTime = newEndTime;
    }
  }



  /// Creates appointment from form data using new backend DTO format
  Future<AppointmentCreationResult> createAppointment(AppointmentFormData formData) async {
    try {
      // Get service name to ID mapping from CalendarProvider
      final serviceNameToIdMap = calendarProvider.getServiceNameToIdMap();

      // Use the new ScheduleAppointmentRequest DTO format with real service IDs
      final scheduleRequest = formData.toScheduleAppointmentRequest(serviceNameToIdMap);
      return await calendarProvider.addAppointmentFromFormData(scheduleRequest);
    } catch (e) {
      return AppointmentCreationResult.failure('Error creating appointment: $e');
    }
  }

  /// Validates form data using enhanced backend validation
  String? validateForm(AppointmentFormData formData) {
    // Use the enhanced backend validation
    final backendValidation = formData.validateForBackend();
    if (backendValidation != null) {
      return backendValidation;
    }

    // Phone validation
    if (!PhoneNumberUtils.isValidRomanianMobile(formData.clientPhone)) {
      return 'Vă rugăm să introduceți un număr de telefon valid';
    }

    // Working hours validation
    final settings = calendarProvider.workingHoursSettings;
    if (settings != null) {
      final withinHours = settings.isTimeRangeWithinWorkingHours(
        formData.startTime,
        formData.endTime,
      );
      if (!withinHours) {
        return 'Programarea trebuie să fie în intervalul orar de lucru';
      }
    }

    return null;
  }



  /// Handles client type change
  void handleClientTypeChange(AppointmentFormData formData, bool isExisting) {
    if (isExisting) {
      formData.isExistingClient = true;

      // Restore previous selection if available
      if (formData.savedClientId != null && formData.savedClientId!.isNotEmpty) {
        formData.clientId = formData.savedClientId!;
        formData.clientName = formData.savedClientName ?? '';
        formData.clientPhone = formData.savedClientPhone ?? '';
        formData.clientPets = [...formData.savedClientPets];
      } else {
        // Reset to empty selection - let user choose
        formData.clientId = '';
        formData.clientName = '';
        formData.clientPhone = '';
        formData.clientPets = [];
      }
      formData.petId = '';
      formData.petName = '';
      formData.petSpecies = 'dog';
    } else {
      // Save current selection before switching to new client mode
      if (!formData.isNewClient && formData.clientId.isNotEmpty) {
        formData.savedClientId = formData.clientId;
        formData.savedClientName = formData.clientName;
        formData.savedClientPhone = formData.clientPhone;
        formData.savedClientPets = [...formData.clientPets];
      }

      formData.isExistingClient = false;
      formData.resetToNewClient();
    }
  }

  /// Handles client selection
  Future<void> handleClientSelection(AppointmentFormData formData, Client client) async {
    debugPrint('🔄 AppointmentFormLogic: Handling client selection: ${client.name} (${client.id})');

    // Update client data (this also clears pets)
    formData.updateClientData(client);

    // Load pets for the selected client
    await loadPetsForClient(formData, client.id);

    debugPrint('✅ AppointmentFormLogic: Client selection completed');
  }

  /// Handles adding new pet
  void handleAddNewPet(AppointmentFormData formData) {
    formData.resetToNewPet();
  }

  /// Toggles between new pet mode and existing pet selection
  void handleToggleNewPet(AppointmentFormData formData) {
    if (formData.isNewPet) {
      if (formData.clientPets.isNotEmpty) {
        formData.updatePetData(formData.clientPets.first);
      } else {
        formData.petId = '';
        formData.petName = '';
        formData.petSpecies = 'dog';
        formData.petBreed = '';
        formData.petSize = 'M';
      }
    } else {
      formData.resetToNewPet();
    }
  }

  /// Handles pet selection
  void handlePetSelection(AppointmentFormData formData, Pet pet) {
    formData.updatePetData(pet);
  }

  /// Handles breed change for new pet
  void handlePetBreedChange(AppointmentFormData formData, String breed) {
    final oldSize = formData.petSize;
    formData.petBreed = breed;
    // Only update species and size if the breed is recognized. This prevents
    // accidentally switching to "other" species while the user is typing and
    // hasn't selected a full breed name yet.
    final isKnownBreed =
        AppointmentFormConstants.dogBreeds.containsKey(breed) ||
            AppointmentFormConstants.catBreeds.containsKey(breed);
    if (isKnownBreed) {
      formData.petSpecies = AppointmentFormConstants.getBreedSpecies(breed);
      formData.petSize = AppointmentFormConstants.getBreedSize(breed);
    }

    // Debug logging for breed-to-size mapping
    debugPrint('🐕 Breed changed: $breed');
    debugPrint('📏 Size mapping: $oldSize → ${formData.petSize}');

    if (oldSize != formData.petSize) {
      debugPrint('💰 Size change detected - prices will be recalculated');
    }
  }

  /// Handles adding a service
  Future<void> handleAddService(AppointmentFormData formData, String service) async {
    formData.addService(service);
    await updateEndTimeBasedOnServices(formData);
  }

  /// Handles removing a service
  Future<void> handleRemoveService(AppointmentFormData formData, String service) async {
    formData.removeService(service);
    await updateEndTimeBasedOnServices(formData);
  }

  /// Handles start time change
  Future<void> handleStartTimeChange(AppointmentFormData formData, DateTime startTime) async {
    formData.startTime = startTime;
    await updateEndTimeBasedOnServices(formData);
  }

  /// Handles appointment date change
  Future<void> handleAppointmentDateChange(AppointmentFormData formData, DateTime newDate) async {
    formData.appointmentDate = newDate;

    // Update start and end times to the new date
    formData.startTime = DateTime(
      newDate.year,
      newDate.month,
      newDate.day,
      formData.startTime.hour,
      formData.startTime.minute,
    );

    await updateEndTimeBasedOnServices(formData);
  }

  /// Handles staff assignment change
  void handleStaffChange(AppointmentFormData formData, String staffId) {
    final staff = formData.availableStaff
        .where((s) => s.id == staffId)
        .isNotEmpty
        ? formData.availableStaff
            .where((s) => s.id == staffId)
            .first
        : null;

    if (staff != null) {
      formData.updateStaffData(staff);
    }
  }

  /// Handles notes visibility change
  void handleNotesVisibilityChange(AppointmentFormData formData, bool showNotes) {
    formData.showNotes = showNotes;
    if (!showNotes) {
      formData.notes = '';
    }
  }

  /// Handles repetition visibility change
  void handleRepetitionVisibilityChange(AppointmentFormData formData, bool showRepetition) {
    formData.showRepetition = showRepetition;
    if (!showRepetition) {
      formData.repetitionFrequency = 'none';
    }
  }

  /// Handles repetition frequency change
  void handleRepetitionFrequencyChange(AppointmentFormData formData, String frequency) {
    formData.repetitionFrequency = frequency;
  }
}
